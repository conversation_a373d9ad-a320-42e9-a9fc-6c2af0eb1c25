from __future__ import print_function
import boto3
from rest_framework import status
from moviepy.editor import <PERSON>FileClip, AudioFileClip
import tempfile
import uuid
import traceback
from moviepy.editor import VideoFileClip, AudioFileClip, vfx
from rest_framework import generics, status, views
from django.http import JsonResponse, HttpResponse
from cu_admin.user_notifications import *
from cu_app.utils.helper_functions import *
from ..serializers import *
from datetime import *
from rest_framework.response import Response
import json
from django.contrib.auth.models import Group, Permission
from django.views import View
from django.contrib.contenttypes.models import ContentType
from django.contrib.auth import get_user_model
from ..models import *
import os
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from rest_framework.views import APIView
import os.path

import re

# from datetime import datetime,date, timedelta,timezone
from django.conf import settings
from ..forms import *
from ..cu_library import *

# aws sns
import logging
from dotenv import load_dotenv
import json
from django.core.files.uploadedfile import InMemoryUploadedFile, TemporaryUploadedFile
from django.db.models import Q
import requests

# shotstack
import shotstack_sdk as shotstack
import os
from shotstack_sdk.model.soundtrack import Soundtrack
from shotstack_sdk.api import edit_api
from shotstack_sdk.model.clip import Clip
from shotstack_sdk.model.track import Track
from shotstack_sdk.model.timeline import Timeline
from shotstack_sdk.model.output import Output
from shotstack_sdk.model.edit import Edit
from shotstack_sdk.model.video_asset import VideoAsset
from shotstack_sdk.model.transition import Transition
from shotstack_sdk.model.title_asset import TitleAsset
from django.utils import timezone
import zoneinfo
from django.utils.dateparse import parse_datetime
import sendgrid
from sendgrid.helpers.mail import *

# added
from django.contrib.auth.hashers import check_password, make_password
from django.core.paginator import Paginator
from firebase_admin.messaging import (
    Message,
    Notification
)
from fcm_django.models import FCMDevice
from python_http_client.exceptions import HTTPError
from ..cu_library import *
# from cu_admin.views.views1 import AdminApprovePushNotification
from django.db.models import Sum
import calendar
import urllib.parse
from cu_project.common.utils import generate_response

ENV_ROOT = os.path.join(settings.BASE_DIR, ".env")
load_dotenv(ENV_ROOT)

logger = logging.getLogger(__name__)


def serialize_model(a, serializer):

    result = {k: v for k, v in serializer(a).data.items()}
    print(f"in filter model data   {result}")
    return result


def get_expertise_data(expertise):
    p_data1 = []
    for x in expertise:
        p_data1.append(
            serialize_model(
                ExpertiseCancertype.objects.filter(id__exact=x.id)[0],
                ExpertiseCancertypeSerializer,
            )
        )

    return p_data1


def filter_user_data(a):
    keys = [
        "is_admin",
        "is_active",
        "is_superuser",
        "password",
        "user_permissions",
        "groups",
        "PWVerifyCode",
        "PWCodeGentime",
    ]
    result = {
        k: v for k, v in CuUserRegisterSerializer(a).data.items() if k not in keys
    }
    print(
        f"in filter userrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrr data   {a.groups.all()}"
    )
    result["role"] = a.groups.all()[0].name
    if (
        result["role"] == "doctor"
        or result["role"] == "researcher"
        or result["role"] == "influencer"
    ):
        print(
            f"other details dataaaaa-----{a.doctordetails}---{type(a.doctordetails)}")
        result["expertise"] = get_expertise_data(a.expertise.all())
        result["doctor_other_details"] = serialize_model(
            a.doctordetails, DoctorDetailsSerializer
        )

    elif result["role"] == "patient":
        result["patient_other_details"] = serialize_model(
            a.patientdetails, PatientDetailsSerializer
        )

    else:
        pass
    return result


# def upload_cerificates(c,d):
#     file_urls = []
#     print(f"is files present---{len(c)}")
#     if len(c) > 0:
#         for x in c:
#             file_urls.append(d + handle_uploaded_file(c[x]))
#         print(f"file urllll----{file_urls}")
#     return file_urls


def UpdatePatientData(a, b, c, d):
    print(f"files patients------{c}----{type(c)}")

    d_id = CuUser.objects.get(email=b["email"])  # get user object
    patient_obj = PatientDetails.objects.filter(PatientId=d_id)[
        0
    ]  # get patient details object
    # update the relevant fields using loop
    for x in a:

        if not isinstance(a[x], InMemoryUploadedFile) and not x in [
            "phone",
            "date_of_birth",
            "name",
            "email",
            "sex",
            "age",
            "TimeZone",
            "City",
            "Country",
            "ProfilePhoto",
            "Signature",
        ]:
            setattr(patient_obj, x, a[x])

    res = patient_obj.save()

    for x in c:
        print(f"in loop patient files---{x}--{c[x]}")
        if x == "ProfilePhoto":
            print(f"yessss-----------{x}")
            b1 = handle_uploaded_file(c[x])
            f_name = b1.split("/")[2]
            file_url = settings.PROJECT_ROOT + b1
            file_urls_res = handle_s3_uploaded_file(f_name, file_url)
            patient_obj.ProfilePhoto = file_urls_res
        elif x == "Signature":
            b1 = handle_uploaded_file(c[x])
            f_name = b1.split("/")[2]
            file_url = settings.PROJECT_ROOT + b1
            file_urls_res = handle_s3_uploaded_file(f_name, file_url)
            patient_obj.Signature = file_urls_res
        else:
            file_urls_res = []

        print(f"patienttttttt-------filesulrs---{file_urls_res}")

    res = patient_obj.save()

    return True


# added
def UpdateAdminData(a, b, c, d):
    print(f"files admin------{c}----{type(c)}")

    d_id = CuUser.objects.get(email=b["email"])  # get user object
    admin_obj = AdminDetails.objects.filter(AdminId=d_id)[
        0
    ]  # get patient details object
    # update the relevant fields using loop
    for x in a:

        if not isinstance(a[x], InMemoryUploadedFile) and not x in [
            "phone",
            "date_of_birth",
            "name",
            "email",
            "sex",
            "age",
            "TimeZone",
            "City",
            "Country",
            "ProfilePhoto",
        ]:
            setattr(admin_obj, x, a[x])

    res = admin_obj.save()
    # added
    if "current_password" in a and "new_password" in a:
        password_match = check_password(a["current_password"], d_id.password)
        if password_match:
            hashed_password = make_password(a["new_password"])
            d_id.password = hashed_password
            d_id.save()
        else:
            return False
    # added
    for x in c:
        print(f"in loop patient files---{x}--{c[x]}")
        if x == "ProfilePhoto":
            print(f"yessss-----------{x}")
            b1 = handle_uploaded_file(c[x])
            f_name = b1.split("/")[2]
            file_url = settings.PROJECT_ROOT + b1
            file_urls_res = handle_s3_uploaded_file(f_name, file_url)
            admin_obj.ProfilePhoto = file_urls_res
        # elif x == "Signature":
        #     b1 = handle_uploaded_file(c[x])
        #     f_name = b1.split('/')[2]
        #     file_url = settings.PROJECT_ROOT + b1
        #     file_urls_res = handle_s3_uploaded_file(f_name, file_url)
        #     patient_obj.Signature = file_urls_res
        else:
            file_urls_res = []

        print(f"patienttttttt-------filesulrs---{file_urls_res}")

    res = admin_obj.save()

    return True


# added


def SaveS3KeyObj(a):
    print(f"signedkeyyyy{a}")
    res = S3ObjectsKeys.objects.create(Key=a)

    return res


# add role to user
def AddUserRole(a, role):
    try:
        ct = ContentType.objects.get_for_model(cu_permissions)
        p1 = Permission.objects.get_or_create(
            codename="cu_patient_permissions",
            name="CU patient permissions",
            content_type=ct,
        )
        g1 = Group.objects.get_or_create(name=role)
        g1[0].permissions.add(p1[0])
        if len(a.groups.all()) > 1:
            return False
        else:
            a.groups.add(g1[0])
        return a
    except Exception as e:
        print("add role exceptionsss", e)
        try:
            us = get_user_model().objects.get(email=a["email"]).delete()
            print(f"user deleted role...{us}---{get_user_model()}")
        except Exception as f:
            print("delete user role exceptionsss", f)
        return str(e)


def UpdateDoctorData(a, b, c, d):
    print(f"files doctors----------{c}----{type(c)}")
    d_id = CuUser.objects.get(email=b["email"])  # get user object
    doctor_obj = DoctorDetails.objects.filter(
        DoctorId=d_id)[0]  # get details object
    # update the relevant fields using loop
    for x in a:
        if not isinstance(a[x], InMemoryUploadedFile) and not x in [
            "expertise_name",
            "phone",
            "date_of_birth",
            "name",
            "email",
            "sex",
            "age",
            "TimeZone",
            "cancer_type",
            "City",
            "Country",
            "ProfilePhoto",
            "Signature",
            "SocialLinks",
        ]:
            setattr(doctor_obj, x, a[x])
        if x in ["ExperienceSummary", "ResearchPapers", "SocialLinks", "Reason"]:
            setattr(doctor_obj, x, json.loads(a[x]))

    if "Reason" in a:
        doctor_obj.ReasonDate = timezone.now()
    # user role set
    if "user_role" in a and d_id.approval in ["pending"]:
        azz = AddUserRole(d_id, a["user_role"])
        print(f"-------------------user_role-----------{azz}")
    res = doctor_obj.save()

    # add multiple certs
    # r = re.compile(r"^Certificates")
    # file_list_keys = list(filter(r.match, c.keys()))
    # file_list_values = [y for x, y in c.items() if x in file_list_keys]
    # data_list_keys = list(filter(r.match, a.keys()))
    # data_list_values = [y for x,y in a.items() if x in data_list_keys and x not in file_list_keys]

    # print(f"regerxxxxxxxxxxxxxxxxxx{data_list_values}--")

    # certs_len=len(file_list_values)
    # certs = []

    # i = 0

    # print(f"length---{certs_len}")

    # for x in file_list_values:
    #         certi=[]
    #         b1 = handle_uploaded_file(x)
    #         f_name = b1.split('/')[2]
    #         file_url = settings.PROJECT_ROOT + b1
    #         file_urls_res = handle_s3_uploaded_file(f_name, file_url)
    #         certi.append(a[f'Certi_name[{i}]'])
    #         certi.append(file_urls_res)
    #         certs.append(certi)
    #         i+=1

    # for x in data_list_values:
    #     val1=x.split('/')[3]
    #     val2=val1.split('?')[0]
    #     certi.append(a[f'Certi_name[{i}]'])
    #     certi.append(val2)
    #     certs.append(certi)
    #     i+=1

    # print(f"certificates---{certs}")
    # if i>0:
    #     doctor_obj.Certificates = certs
    i = 0
    y = 0
    z = 0
    certs = []
    achievements = []
    while i < len(a):
        if f"Certificates[{i}]" in a:
            y = 1
            certi = []
            print(type(a[f"Certificates[{i}]"]))
            if type(a[f"Certificates[{i}]"]) == str:
                b = a[f"Certificates[{i}]"]
                b = b.split("/")[3]
                b = b.split("?")[0]
                b = urllib.parse.unquote(b)

                # Second decoding
                second_decoded = urllib.parse.unquote(b)

                print("Original String:", b)
                print("First Decoded:", b)
                print("Second Decoded:", second_decoded)
                certi.append(a[f"Certi_name[{i}]"])
                certi.append(b)
                certs.append(certi)
            elif (
                type(a[f"Certificates[{i}]"]) == InMemoryUploadedFile
                or type(a[f"Certificates[{i}]"]) == TemporaryUploadedFile
            ):

                b1 = handle_uploaded_file(a[f"Certificates[{i}]"])
                f_name = b1.split("/")[2]
                file_url = settings.PROJECT_ROOT + b1

                file_urls_res = handle_s3_uploaded_file(f_name, file_url)
                print(f"urlsssssssssss{file_urls_res}")
                certi.append(a[f"Certi_name[{i}]"])
                certi.append(file_urls_res)
                certs.append(certi)
            else:
                pass
        if f"OtherAchievements[{i}]" in a:
            z = 1
            achi = []
            print(type(a[f"OtherAchievements[{i}]"]))
            if type(a[f"OtherAchievements[{i}]"]) == str:
                b = a[f"OtherAchievements[{i}]"]
                b = b.split("/")[3]
                b = b.split("?")[0]
                b = urllib.parse.unquote(b)

                # Second decoding
                second_decoded = urllib.parse.unquote(b)

                print("Original String:", b)
                print("First Decoded:", b)
                print("Second Decoded:", second_decoded)
                achi.append(a[f"Achievements_name[{i}]"])
                achi.append(b)
                achievements.append(achi)
            elif (
                type(a[f"OtherAchievements[{i}]"]) == InMemoryUploadedFile
                or type(a[f"OtherAchievements[{i}]"]) == TemporaryUploadedFile
            ):

                b1 = handle_uploaded_file(a[f"OtherAchievements[{i}]"])
                f_name = b1.split("/")[2]
                file_url = settings.PROJECT_ROOT + b1

                file_urls_res = handle_s3_uploaded_file(f_name, file_url)
                print(f"urlsssssssssss{file_urls_res}")
                achi.append(a[f"Achievements_name[{i}]"])
                achi.append(file_urls_res)
                achievements.append(achi)
                print(f"aaaaaaaaaaaaaaaaaa{achievements}")
            else:
                pass
        i += 1
    # add multiple certs ends
    if y > 0:
        doctor_obj.Certificates = certs

    # r = re.compile(r"^OtherAchievements")
    # file_list_keys = list(filter(r.match, c.keys()))
    # file_list_values = [y for x, y in c.items() if x in file_list_keys]
    # data_list_keys = list(filter(r.match, a.keys()))
    # data_list_values = [y for x,y in a.items() if x in data_list_keys and x not in file_list_keys]

    # print(f"regerxxxxxxxxxxxxxxxxxx{data_list_values}--")

    # other_ach_len=len(file_list_values)
    # achievements = []

    # i = 0
    # print(f"length---{other_ach_len}")

    # for x in file_list_values:
    #         achi=[]
    #         b1 = handle_uploaded_file(x)
    #         f_name = b1.split('/')[2]
    #         file_url = settings.PROJECT_ROOT + b1
    #         file_urls_res = handle_s3_uploaded_file(f_name, file_url)
    #         achi.append(a[f'Achievements_name[{i}]'])
    #         achi.append(file_urls_res)
    #         achievements.append(achi)
    #         i+=1

    # for x in data_list_values:
    #     achi=[]
    #     val1=x.split('/')[3]
    #     val2=val1.split('?')[0]
    #     achi.append(a[f'Achievement_name[{i}]'])
    #     achi.append(val2)
    #     achievements.append(achi)
    #     i+=1

    # print(f"achievements---{achievements}")
    print(achievements)
    if z > 0:
        doctor_obj.OtherAchievements = achievements
    print(doctor_obj.OtherAchievements)

    for x in c:
        print(f"in loop doctor files---{x}--{c[x]}")
        if x == "ProfilePhoto":
            b1 = handle_uploaded_file(c[x])
            f_name = b1.split("/")[2]
            file_url = settings.PROJECT_ROOT + b1

            file_urls_res = handle_s3_uploaded_file(f_name, file_url)
            print(f"urlsssssssssss{file_urls_res}")
            doctor_obj.ProfilePhoto = file_urls_res

        elif x == "Signature":
            b1 = handle_uploaded_file(c[x])
            f_name = b1.split("/")[2]
            file_url = settings.PROJECT_ROOT + b1
            file_urls_res = handle_s3_uploaded_file(f_name, file_url)
            doctor_obj.Signature = file_urls_res
        else:
            file_urls_res = []
    res = doctor_obj.save()
    return True


def SendPushNotification():
    device = FCMDevice.objects.all()
    print(f"fcm devices----{device}")
    a = device.send_message(
        Message(notification=Notification(title="title", body="message"))
    )
    print(f"push----{a}")
    return a


# added
class UserUpdate(generics.RetrieveUpdateAPIView):
    queryset = CuUser.objects.all()
    lookup_field = "email"
    serializer_class = CuUserRegisterSerializer

    def partial_update(self, request, *args, **kwargs):
        instance = self.get_object()

        # Restrict phone number change
        if "phone" in request.data:
            if request.data["phone"] != instance.phone:
                # Force the phone back to its original value
                mutable = getattr(request.data, "_mutable", False)
                if not mutable and hasattr(request.data, "_mutable"):
                    request.data._mutable = True
                request.data["phone"] = instance.phone
                if hasattr(request.data, "_mutable"):
                    request.data._mutable = False

        if get_cu_user_type(instance.id) == "patient":
            if (
                "cancer_type_main" in request.data
                and "cancer_type_sub" in request.data
                and "cancer_type_level" in request.data
            ):
                t1 = (
                    request.data["cancer_type_main"],
                    request.data["cancer_type_sub"],
                    request.data["cancer_type_level"],
                )
                l1 = list(t1)
                instance.cancer_type = l1
                instance.save()
                return super().partial_update(request, *args, **kwargs)
            else:
                return super().partial_update(request, *args, **kwargs)

        return super().partial_update(request, *args, **kwargs)

    def put(self, request, *args, **kwargs):
        print(f"{self.kwargs.get('email')}")
        a = self.partial_update(request, *args, **kwargs)
        # approval request
        if "ApprovalRequest" in request.data:
            if int(request.data["ApprovalRequest"]) == 1:
                print("yesssssssss")
                instance = self.get_object()
                instance.approval = "Approval_requested"
                instance.save(update_fields=["approval"])

                # noti_check = notification_check("Profile Approval Request!")  # Check if notification is enabled
                # if noti_check:
                try:
                    user = get_user_model().objects.get(email__exact=self.kwargs.get("email"))
                    expert_id = user.id
                    print("user", user)
                    notificaton = NotifyAdminForApproval(
                        "Profile Approval Request submission!", expert_id, "patientApprovals")
                    print("this is tenotificatio n", notificaton)
                    print(
                        f"Notification sent for profile approval request by {expert_id}")
                except Exception as e:
                    logger.error(f"Error sending notification: {str(e)}")

        # approval request ends
        id_val = CuUser.objects.filter(
            email__exact=self.kwargs.get("email"))[0].id
        user_role = get_cu_user_type(id_val)
        print(f"updateeee----------{user_role}")
        if a.status_code == 200:
            if user_role == "patient":
                if "ApprovalRequest" in request.data:
                    if int(request.data["ApprovalRequest"]) == 1:
                        # inserting patient data into zoho contacts
                        acc_zoho = AddToZohoContacts(
                            CuUser.objects.get(
                                email__exact=self.kwargs["email"])
                        )
                # added for patient reactivation request
                if "StatusChangeReason" in request.data:
                    StatusReason.objects.create(
                        ExpertId=get_user_model().objects.get(
                            email__exact=self.kwargs.get("email")
                        ),
                        Reason=request.data["StatusChangeReason"],
                        ReasonType="Reactivation",
                        ReasonCategory="user_reactivation",
                    )
                upd_status = UpdatePatientData(
                    request.data, kwargs, request.FILES, request.META["HTTP_HOST"]
                )
                if upd_status:
                    dict1 = {}
                    dict1["user_update_status"] = True
                    return Response(json.dumps(dict1))
                else:
                    return Response(json.dumps({"message": "could not update"}))
            # added
            elif user_role in ["admin", "child_admin"]:
                upd_status = UpdateAdminData(
                    request.data, kwargs, request.FILES, request.META["HTTP_HOST"]
                )
                if upd_status:
                    dict1 = {}
                    dict1["user_update_status"] = True
                    return Response(json.dumps(dict1))
                else:
                    return HttpResponse("incorrect current password", status=403)
            # added
            elif (
                user_role == "doctor"
                or user_role == "researcher"
                or user_role == "influencer"
            ):
                if "ApprovalRequest" in request.data:
                    if int(request.data["ApprovalRequest"]) == 1:
                        # inserting doctor data into zoho accounts
                        acc_zoho = AddToZoho(
                            CuUser.objects.get(
                                email__exact=self.kwargs["email"])
                        )

                upd_status = UpdateDoctorData(
                    request.data, kwargs, request.FILES, request.META["HTTP_HOST"]
                )
                if upd_status:
                    # update expertise data of expert
                    b = get_user_model().objects.get(email=a.data["email"])
                    b.expertise.clear()
                    exp_Arr = []
                    i = 0
                    while True:

                        a = request.data.get(f"expertise_name[{i}]", "na")
                        print(f"------{i}------------{a}")
                        if a == "na":
                            break
                        exp_Arr.append(request.data[f"expertise_name[{i}]"])
                        i += 1

                    print(f"arrayyyyy---{exp_Arr}")
                    dict1 = {}
                    for x in exp_Arr:
                        e1 = ExpertiseCancertype.objects.filter(name=x).first()
                        if e1:
                            b.expertise.add(e1)
                            res = b.save()
                            print(f"ressss---{res}--{type(res)}")

                        else:
                            dict1["message"] = "updated but no expertise data found"
                            print(f"not foundddddddddddd")
                            break
                    dict1["user_update_status"] = True
                    return Response(json.dumps(dict1))

                else:
                    return Response(json.dumps({"message": "could not update"}))

        else:

            return Response(json.dumps({"message": "could'nt update"}))


# def get_cu_user_type(a):
#     try:
#         print(f"in user type---{a}")
#         b = get_user_model().objects.get(id=a)
#         u_groups = b.groups.all()
#         print(f"user groups----{u_groups}")
#         if len(u_groups) >= 1:

#             return u_groups[0].name
#         else:
#             return "no valid user found"
#     except Exception as e:
#         print(f"get user exception----{e}")
#         return str(e)


def get_cu_user(a):
    return get_user_model().objects.get(id=a)


class AddExpertise(generics.ListCreateAPIView):

    queryset = ExpertiseCancertype.objects.all()
    serializer_class = ExpertiseCancertypeSerializer

    def post(self, request, *args, **kwargs):

        serializer = ExpertiseCancertypeSerializer(data=request.data)
        serializer.is_valid(
            raise_exception=True
        )  # model field validation automatically applied
        b = serializer.save()
        res = serialize_model(b, ExpertiseCancertypeSerializer)
        return JsonResponse({"message": "expertise added ", "expertise details": res})

    def get(self, request, *args, **kwargs):

        res = self.list(request, *args, **kwargs)
        return res


class GetDoctorAppointmentsDateFilter(generics.ListAPIView):

    serializer_class = SlotSerializer

    def get_queryset(self):

        start_date = timezone.make_aware(
            parse_datetime(self.kwargs.get("start_date")),
            timezone.get_current_timezone(),
        )
        end_date = timezone.make_aware(
            parse_datetime(self.kwargs.get("end_date")
                           ), timezone.get_current_timezone()
        )

        d = get_cu_user(self.kwargs.get("doctor_id"))
        data = SchedulerSlots.objects.filter(
            doctor__exact=d,
            schedule_start_time__gte=start_date,
            schedule_end_time__lte=end_date,
            appointments__isnull=False,
        )
        return data.order_by("-schedule_start_time")

    def get(self, request, *args, **kwargs):

        res = self.list(request, *args, **kwargs)
        # Get the page number from the request
        page_number = request.GET.get("page", 1)
        # Get the number of items per page from the request
        items_per_page = request.GET.get("per_page", 10)
        total_items = len(res.data)
        paginator = Paginator(res.data, items_per_page)
        print(paginator)
        if int(page_number) not in range(1, int(paginator.num_pages) + 1):
            return HttpResponse("Not a valid page number", status=400)
        res.data = paginator.page(page_number)
        app_data_items = []

        for x in res.data:
            p_data = filter_user_data(
                SchedulerSlots.objects.filter(id__exact=x["id"])[
                    0].appointments.patient
            )
            print(f"x-----{x}--{p_data}--{type(p_data)}")
            p_data1 = serialize_model(
                SchedulerSlots.objects.filter(id__exact=x["id"])[
                    0].appointments,
                AppointmentsSerializer,
            )
            meeting_session_details = Appointments.objects.filter(
                id__exact=p_data1["id"]
            )[0].meetingsession_set.all()
            meeting_consent_details = Appointments.objects.filter(
                id__exact=p_data1["id"]
            )[0].appointmentconsent_set.all()
            print(
                f"xyyyyy-----{p_data1}--{type(p_data1)}---------{type(x['schedule_start_time'])}"
            )
            x["appointment_details"] = p_data1
            x["patient"] = p_data
            x["meeting_session_details"] = [
                serialize_model(t, MeetingSessionSerializer)
                for t in meeting_session_details
            ]
            x["meeting_consent_details"] = [
                serialize_model(s, AppointmentConsentSerializer)
                for s in meeting_consent_details
            ]

            current_status = ""
            if x["appointment_details"]["status"] in ["B", "R"]:
                if (
                    timezone.localtime(
                        timezone.now(), timezone.get_current_timezone()
                    ).timestamp()
                    >= timezone.localtime(
                        parse_datetime(x["schedule_end_time"]),
                        timezone.get_current_timezone(),
                    ).timestamp()
                ):
                    if meeting_session_details[0].MeetingStatus == 0:
                        current_status = "Expired"
                    elif meeting_session_details[0].MeetingStatus == 2:
                        current_status = "Completed"
                    elif meeting_session_details[0].MeetingStatus == 1:
                        current_status = "Ongoing"
                    # added
                    elif meeting_session_details[0].MeetingStatus == 3:
                        current_status = "Unattended"
                    # --------------------------------------------------------------
                    else:
                        pass
                # added
                elif meeting_session_details[0].MeetingStatus == 3:
                    current_status = "Unattended"

                elif meeting_session_details[0].MeetingStatus == 2:
                    current_status = "Completed"
                # --------------------------------------------------------------
                else:
                    current_status = "Upcoming"

            x["current_status"] = current_status
            app_data_items.append(x)
        response_data = {
            "total_items": total_items,
            "total_pages": paginator.num_pages,
            "items": app_data_items,
        }
        return JsonResponse(response_data)


class GetDoctorFreeSlotsDateFilter(generics.ListAPIView):

    serializer_class = SlotSerializer

    def get_queryset(self):
        # start_date = datetime.strptime(self.kwargs.get('start_date'), "%Y-%m-%d")
        # end_date = datetime.strptime(self.kwargs.get('end_date'), "%Y-%m-%d")
        start_date = timezone.make_aware(
            parse_datetime(self.kwargs.get("start_date")),
            timezone.get_current_timezone(),
        )
        end_date = timezone.make_aware(
            parse_datetime(self.kwargs.get("end_date")
                           ), timezone.get_current_timezone()
        )
        d = get_cu_user(self.kwargs.get("doctor_id"))
        data = SchedulerSlots.objects.filter(
            doctor__exact=d,
            schedule_start_time__gte=start_date,
            schedule_end_time__lte=end_date,
            appointments__isnull=True,
        )
        data = data.filter(~Q(status__exact="C"))
        return data

    def get(self, request, *args, **kwargs):
        print(
            f"res22----{kwargs['start_date']}---{kwargs['end_date']}-{kwargs['doctor_id']}"
        )
        res = self.list(request, *args, **kwargs)
        print(f"res222-333----{res.data}--{type(res.data)}----")
        return res


class GetAnAppointmentDetails(generics.ListAPIView):
    serializer_class = SlotSerializer

    def get_queryset(self):
        app_id = self.kwargs.get("app_id")

        data = SchedulerSlots.objects.filter(appointments__id=app_id)

        return data

    def get(self, request, *args, **kwargs):
        res = self.list(request, *args, **kwargs)
        print(res.data)
        for x in res.data:
            # p_data = filter_user_data(SchedulerSlots.objects.filter(id__exact=x['id'])[0].appointments.patient)

            p_data1 = serialize_model(
                SchedulerSlots.objects.filter(id__exact=x["id"])[
                    0].appointments,
                AppointmentsSerializer,
            )

            # p_data2 = filter_user_data(SchedulerSlots.objects.filter(id__exact=x['id'])[0].doctor)
            patient_id = SchedulerSlots.objects.filter(id__exact=x["id"])[
                0
            ].appointments.patient_id
            p_data = serialize_model(
                CuUser.objects.filter(id__exact=patient_id)[
                    0], CuUserSerializer
            )
            doctor_id = SchedulerSlots.objects.filter(
                id__exact=x["id"])[0].doctor_id
            p_data2 = serialize_model(
                CuUser.objects.filter(id__exact=doctor_id)[0], CuUserSerializer
            )
            x["appointment_details"] = p_data1
            x["patient"] = p_data
            x["patient"]["role"] = "patient"
            x["doctor"] = p_data2
            x["doctor"]["role"] = get_cu_user_type(doctor_id)
            consent_details = Appointments.objects.filter(
                id__exact=self.kwargs["app_id"]
            )[0].appointmentconsent_set.all()
            x["consent_details"] = [
                serialize_model(t, AppointmentConsentSerializer)
                for t in consent_details
            ]
            meeting_session_details = Appointments.objects.filter(
                id__exact=p_data1["id"]
            )[0].meetingsession_set.all()
            meeting_consent_details = Appointments.objects.filter(
                id__exact=p_data1["id"]
            )[0].appointmentconsent_set.all()
            current_status = ""
            if x["appointment_details"]["status"] in ["B", "R"]:
                if (
                    timezone.localtime(
                        timezone.now(), timezone.get_current_timezone()
                    ).timestamp()
                    >= timezone.localtime(
                        parse_datetime(x["schedule_end_time"]),
                        timezone.get_current_timezone(),
                    ).timestamp()
                ):
                    if meeting_session_details[0].MeetingStatus == 0:
                        current_status = "Expired"
                    elif meeting_session_details[0].MeetingStatus == 2:
                        current_status = "Completed"
                    elif meeting_session_details[0].MeetingStatus == 1:
                        current_status = "Ongoing"
                    # added
                    elif meeting_session_details[0].MeetingStatus == 3:
                        current_status = "Unattended"
                    # --------------------------------------------------------------
                    else:
                        pass
                # added
                elif meeting_session_details[0].MeetingStatus == 3:
                    current_status = "Unattended"
                elif meeting_session_details[0].MeetingStatus == 2:
                    current_status = "Completed"
                # --------------------------------------------------------------
                else:
                    current_status = "Upcoming"

            x["current_status"] = current_status

        return res


class GetDoctorAppointmentsDayFilter(generics.ListAPIView):
    serializer_class = SlotSerializer

    def get_queryset(self):

        date_val = timezone.make_aware(
            parse_datetime(self.kwargs.get("date_value")),
            timezone.get_current_timezone(),
        )
        next_day = date_val + timedelta(days=1)

        d = get_cu_user(self.kwargs.get("doctor_id"))
        data = SchedulerSlots.objects.filter(
            doctor__exact=d,
            schedule_start_time__gte=date_val,
            schedule_start_time__lt=next_day,
            appointments__isnull=False,
        )
        return data.order_by("-schedule_start_time")

    def get(self, request, *args, **kwargs):
        res = self.list(request, *args, **kwargs)
        for x in res.data:
            p_data = filter_user_data(
                SchedulerSlots.objects.filter(id__exact=x["id"])[
                    0].appointments.patient
            )

            p_data1 = serialize_model(
                SchedulerSlots.objects.filter(id__exact=x["id"])[
                    0].appointments,
                AppointmentsSerializer,
            )
            meeting_session_details = Appointments.objects.filter(
                id__exact=p_data1["id"]
            )[0].meetingsession_set.all()
            meeting_consent_details = Appointments.objects.filter(
                id__exact=p_data1["id"]
            )[0].appointmentconsent_set.all()
            x["appointment_details"] = p_data1
            x["patient"] = p_data
            x["meeting_session_details"] = [
                serialize_model(t, MeetingSessionSerializer)
                for t in meeting_session_details
            ]
            x["meeting_consent_details"] = [
                serialize_model(s, AppointmentConsentSerializer)
                for s in meeting_consent_details
            ]
            current_status = ""
            if x["appointment_details"]["status"] in ["B", "R"]:
                if (
                    timezone.localtime(
                        timezone.now(), timezone.get_current_timezone()
                    ).timestamp()
                    >= timezone.localtime(
                        parse_datetime(x["schedule_end_time"]),
                        timezone.get_current_timezone(),
                    ).timestamp()
                ):

                    if meeting_session_details[0].MeetingStatus == 0:
                        current_status = "Expired"
                    elif meeting_session_details[0].MeetingStatus == 2:
                        current_status = "Completed"
                    elif meeting_session_details[0].MeetingStatus == 1:
                        current_status = "Ongoing"
                    # added
                    elif meeting_session_details[0].MeetingStatus == 3:
                        current_status = "Unattended"
                    # --------------------------------------------------------------
                    else:
                        pass
                # added
                elif meeting_session_details[0].MeetingStatus == 3:
                    current_status = "Unattended"
                elif meeting_session_details[0].MeetingStatus == 2:
                    current_status = "Completed"
                # --------------------------------------------------------------
                else:
                    current_status = "Upcoming"
            if x["appointment_details"]["status"] == "C":
                current_status = "Cancelled"
            x["current_status"] = current_status
        j_app = {
            "appointments_data": {
                "upcoming_consultations": Appointments.objects.filter(
                    slot_id_id__doctor__id__exact=self.kwargs["doctor_id"],
                    status__in=["B", "P", "R"],
                    slot_id_id__schedule_start_time__gte=timezone.now(),
                ).count(),
                "cancelled_consultations": Appointments.objects.filter(
                    slot_id_id__doctor__id__exact=self.kwargs["doctor_id"], status="C"
                ).count(),
                "completed_consultations": MeetingSession.objects.filter(
                    AppointmentId__slot_id_id__doctor__id__exact=self.kwargs[
                        "doctor_id"
                    ],
                    MeetingStatus=2,
                ).count(),
            }
        }

        res.data.append(j_app)
        # res.data['upcoming_requests']=u_r
        return res


class PrescriptionView(generics.CreateAPIView):
    serializer_class = PrescriptionSerializer

    def create(self, request, *args, **kwargs):

        expert_id = Appointments.objects.get(
            id__exact=request.data["AppointmentId"]
        ).slot_id.doctor.id
        user_role = get_cu_user_type(expert_id)

        if user_role not in ["doctor"]:
            return "invalid user"
        exp_signature = DoctorDetails.objects.get(
            DoctorId__exact=expert_id).Signature
        if exp_signature is not None:

            print(f"prescription create------------{exp_signature}")
            new_obj_url = get_s3_signed_url_bykey(exp_signature)

        request.data["DoctorSignature"] = (
            new_obj_url if exp_signature is not None else ""
        )

        return super().create(request, *args, **kwargs)

    def post(self, request, *args, **kwargs):

        res = self.create(request, *args, **kwargs)

        if isinstance(res, Response):
            # add oral medication
            for x in request.data["oral_data"]:
                MedicineName, DoseStrength, Frequency, Duration, Remarks = x
                a = OralMedication.objects.create(
                    MedicineName=MedicineName,
                    DoseStrength=DoseStrength,
                    Frequency=Frequency,
                    Duration=Duration,
                    Remarks=Remarks,
                    PrescriptionId=Prescription.objects.get(
                        id__exact=res.data["id"]),
                )

            # add iv medication
            for x in request.data["iv_data"]:
                MedicineName, DoseStrength, ModeOfAdministration, Frequency, Remarks = x
                a = IVIMMedication.objects.create(
                    MedicineName=MedicineName,
                    DoseStrength=DoseStrength,
                    ModeOfAdministration=ModeOfAdministration,
                    Frequency=Frequency,
                    Remarks=Remarks,
                    PrescriptionId=Prescription.objects.get(
                        id__exact=res.data["id"]),
                )

            return Response({"prescription_id": res.data["id"]})
        else:
            return Response({"message": res})


class IRPrescriptionView(generics.CreateAPIView):
    serializer_class = IRPrescriptionSerializer

    def create(self, request, *args, **kwargs):
        print(f"iilllll")
        # needed for form data
        # request.data._mutable = True
        expert_id = Appointments.objects.get(
            id__exact=request.data["AppointmentId"]
        ).slot_id.doctor.id
        user_role = get_cu_user_type(expert_id)
        if user_role not in ["researcher", "influencer"]:
            return "invalid user"
        exp_signature = DoctorDetails.objects.get(
            DoctorId__exact=expert_id).Signature
        if exp_signature is not None:

            print(f"prescription create------------{exp_signature}")
            new_obj_url = get_s3_signed_url_bykey(exp_signature)

        request.data["DoctorSignature"] = (
            new_obj_url if exp_signature is not None else ""
        )
        print(
            f"user_role-----{request.data['DoctorSignature']}--------------------")
        return super().create(request, *args, **kwargs)

    def post(self, request, *args, **kwargs):
        a = self.create(request, *args, **kwargs)
        print(f"typeeeeee{type(a)}----{isinstance(a,Response)}")
        if isinstance(a, Response):
            y = json.loads(json.dumps(a.data))
            return Response({"prescription_id": y["id"]})
        else:
            return Response({"message": a})


class GetIRPrescriptionView(generics.RetrieveAPIView):
    queryset = IRPrescription.objects.all()
    serializer_class = IRPrescriptionSerializer
    lookup_field = "id"

    def get(self, request, *args, **kwargs):

        a = self.retrieve(request, *args, **kwargs)
        res_data = dict()
        app = Appointments.objects.filter(id__exact=a.data["AppointmentId"])[0]
        p_data = filter_user_data(app.patient)
        d_data = filter_user_data(app.slot_id.doctor)
        #
        if d_data["doctor_other_details"]["Signature"] is not None:

            # is_valid_url = requests.get(d_data['doctor_other_details']['Signature'])
            print(
                f"irrrrrrrrrrr prescription typeeee------------{d_data['doctor_other_details']['Signature']}"
            )
            # if is_valid_url.status_code == 400 or is_valid_url.status_code == 403:
            #     obj_key = S3ObjectsKeys.objects.get(SUrl__exact=d_data['doctor_other_details']['Signature'])
            new_obj_url = get_s3_signed_url_bykey(
                d_data["doctor_other_details"]["Signature"]
            )
            # ddetails = DoctorDetails.objects.filter(DoctorId__exact=d_data['id'])[0]
            # ddetails.Signature = new_obj_url
            # ddetails.save()

            # obj_key.SUrl = new_obj_url
            # obj_key.save()
            d_data["doctor_other_details"]["Signature"] = new_obj_url
        #
        res_data["prescription_data"] = a.data
        res_data["patient_details"] = p_data
        res_data["doctor_details"] = d_data
        res_data["appointment_details"] = serialize_model(
            app, AppointmentsSerializer)
        return JsonResponse(res_data)


class GetPrescriptionView(generics.RetrieveAPIView):
    queryset = Prescription.objects.all()
    serializer_class = PrescriptionSerializer
    lookup_field = "id"

    def get(self, request, *args, **kwargs):

        a = self.retrieve(request, *args, **kwargs)
        medic = Prescription.objects.filter(id__exact=a.data["id"])[0]

        res_data = dict()
        app = Appointments.objects.filter(id__exact=a.data["AppointmentId"])[0]
        p_data = filter_user_data(app.patient)
        d_data = filter_user_data(app.slot_id.doctor)
        #
        if d_data["doctor_other_details"]["Signature"] is not None:

            # is_valid_url = requests.get(d_data['doctor_other_details']['Signature'])
            print(
                f"prescription typeeee------------{d_data['doctor_other_details']['Signature']}"
            )
            # if is_valid_url.status_code == 400 or is_valid_url.status_code == 403:
            #     obj_key = S3ObjectsKeys.objects.get(SUrl__exact=d_data['doctor_other_details']['Signature'])
            new_obj_url = get_s3_signed_url_bykey(
                d_data["doctor_other_details"]["Signature"]
            )
            # ddetails = DoctorDetails.objects.filter(DoctorId__exact=d_data['id'])[0]
            # ddetails.Signature = new_obj_url
            # ddetails.save()

            # obj_key.SUrl = new_obj_url
            # obj_key.save()
            d_data["doctor_other_details"]["Signature"] = new_obj_url
        #
        res_data["prescription_data"] = a.data

        res_data["prescription_data"]["oral_medication"] = OralMedicationSerializer(
            medic.oralmedication_set.all(), many=True
        ).data

        res_data["prescription_data"]["iv_medication"] = IVIMMedicationSerializer(
            medic.ivimmedication_set.all(), many=True
        ).data

        res_data["patient_details"] = p_data
        res_data["doctor_details"] = d_data
        res_data["appointment_details"] = serialize_model(
            app, AppointmentsSerializer)
        return JsonResponse(res_data)


def GetAppDetails(appointment_id):
    data_dict = dict()
    data = SchedulerSlots.objects.filter(appointments__id=appointment_id)[0]

    p_data = filter_user_data(
        SchedulerSlots.objects.filter(id__exact=data.id)[
            0].appointments.patient
    )
    if p_data["patient_other_details"]["ProfilePhoto"] is not None:
        new_obj_url = get_s3_signed_url_bykey(
            p_data["patient_other_details"]["ProfilePhoto"]
        )
        p_data["patient_other_details"]["ProfilePhoto"] = new_obj_url

    p_data1 = dict()
    p_data1["appointment data"] = serialize_model(
        SchedulerSlots.objects.filter(id__exact=data.id)[0].appointments,
        AppointmentsSerializer,
    )
    p_data1["appointment slot details"] = serialize_model(
        SchedulerSlots.objects.filter(id__exact=data.id)[0], SlotSerializer
    )
    p_data2 = filter_user_data(
        SchedulerSlots.objects.filter(id__exact=data.id)[0].doctor
    )
    if p_data2["doctor_other_details"]["ProfilePhoto"] is not None:
        new_obj_url = get_s3_signed_url_bykey(
            p_data2["doctor_other_details"]["ProfilePhoto"]
        )
        p_data2["doctor_other_details"]["ProfilePhoto"] = new_obj_url

    data_dict["appointment_details"] = p_data1
    data_dict["patient"] = p_data
    data_dict["doctor"] = p_data2
    return data_dict


class GetPatientQueries(generics.ListAPIView):

    serializer_class = PatientQueriesSerializer

    def get_queryset(self):
        id_val = self.kwargs.get("id")
        user_role = get_cu_user_type(id_val)
        query_list = PatientQueries.objects.all()
        if "start_date" in self.request.GET:
            start_date = timezone.make_aware(
                parse_datetime(self.request.GET["start_date"]),
                timezone.get_current_timezone(),
            )
            query_list = query_list.filter(QueryTime__gte=start_date)
        if "end_date" in self.request.GET:
            end_date = timezone.make_aware(
                parse_datetime(self.request.GET["end_date"]),
                timezone.get_current_timezone(),
            )
            query_list = query_list.filter(QueryTime__lte=end_date)
        if user_role in ["doctor", "researcher", "influencer"]:
            query_list = query_list.filter(
                ApptId__slot_id__doctor__exact=id_val)
            if "name" in self.request.GET:
                query_list = query_list.filter(
                    ApptId__patient__name__icontains=self.request.GET["name"]
                )
            print(f"get queriessss---------{query_list}----{type(query_list)}")

        elif user_role == "patient":
            query_list = query_list.filter(ApptId__patient_id__exact=id_val)
            if "name" in self.request.GET:
                query_list = query_list.filter(
                    ApptId__slot_id__doctor__name__icontains=self.request.GET["name"]
                )
            print(f"get queriessss---------{query_list}----{type(query_list)}")

        else:
            print(f"----------no data---------")
            query_list = []
            return query_list

        return query_list

    def get(self, request, *args, **kwargs):
        res = self.list(request, *args, **kwargs)
        print(res)
        if "status" in request.GET:
            pass
        else:
            # Get the page number from the request
            page_number = request.GET.get("page", 1)
            # Get the number of items per page from the request
            items_per_page = request.GET.get("per_page", 10)
            total_items = len(res.data)
            paginator = Paginator(res.data, items_per_page)
            if int(page_number) not in range(1, int(paginator.num_pages) + 1):
                return HttpResponse("Not a valid page number", status=400)
            res.data = paginator.page(page_number)
        res_data = []

        for x in res.data:
            queries_replies = []
            z = dict(x)
            qqqq = PatientQueries.objects.filter(
                Q(id__exact=x["id"]) | Q(ReplyTo__exact=x["id"])
            )
            print(f"replies---{x}----------{qqqq.count()}")
            if "status" in request.GET and request.GET["status"] == "pending":
                if qqqq.count() == 1:
                    tr = timezone.now() - qqqq[0].QueryTime
                    print(tr.days)
                    if tr.days in range(0, 4):
                        for o in qqqq:
                            queries_replies.append(
                                serialize_model(o, PatientQueriesSerializer)
                            )
                        q = GetAppDetails(x["ApptId"])
                        z["query_reply_details"] = queries_replies
                        z["app_details"] = q
                        res_data.append(z)
                else:
                    pass
            elif "status" in request.GET and request.GET["status"] == "not_replied":
                if qqqq.count() == 1:
                    tr = timezone.now() - timezone.localtime(
                        qqqq[0].QueryTime, timezone.get_current_timezone()
                    )
                    if tr.days > 3:
                        for o in qqqq:
                            queries_replies.append(
                                serialize_model(o, PatientQueriesSerializer)
                            )
                        q = GetAppDetails(x["ApptId"])
                        z["query_reply_details"] = queries_replies
                        z["app_details"] = q
                        res_data.append(z)
                else:
                    pass
            elif "status" in request.GET and request.GET["status"] == "completed":
                if qqqq.count() > 1:
                    for o in qqqq:
                        queries_replies.append(
                            serialize_model(o, PatientQueriesSerializer)
                        )
                    q = GetAppDetails(x["ApptId"])
                    z["query_reply_details"] = queries_replies
                    z["app_details"] = q
                    res_data.append(z)
                else:
                    pass
            else:
                for o in qqqq:
                    queries_replies.append(
                        serialize_model(o, PatientQueriesSerializer))

                q = GetAppDetails(x["ApptId"])
                z["query_reply_details"] = queries_replies
                z["app_details"] = q
                # print(f"xxxxxxxxxxxx----{q}---------{type(q)}")
                res_data.append(z)
        if "status" in request.GET:
            response_data = {"items": res_data}
        else:

            response_data = {
                "total_items": total_items,
                "total_pages": paginator.num_pages,
                "items": res_data,
            }
        return JsonResponse(response_data)


# added


# class ReplyPatientQuery(generics.CreateAPIView):
#     serializer_class = PatientQueriesSerializer

#     def post(self, request, *args, **kwargs):
#         print(f"doctor reply requesttt------{request.data['ReplyTo']}")
#         reply_exists = PatientQueries.objects.filter(
#             ReplyTo__exact=request.data["ReplyTo"]
#         )
#         if reply_exists.exists():
#             return Response("Patient query has already been replied.")
#         query_obj = PatientQueries.objects.filter(
#             id__exact=request.data["ReplyTo"])[0]
#         resp = self.create(request, *args, **kwargs)
#         print(f"doctor reply------{resp}")
#         # added
#         user_id = query_obj.ApptId.patient.id
#         user_name = query_obj.ApptId.patient.name
#         app_url = os.getenv(
#             "NEXT_CANCER_UNWIRED_PATIENT_APP") + "/myprofile?tab=inbox"
#         r_p_q_n = notification_check("Query replied!")
#         if r_p_q_n == True:
#             zz = SendPushReplyQuery(
#                 request.data["ReplyTo"], app_url, user_id, user_name
#             )
#             print(f"----------notification_query_replied-------{zz}")
#         else:
#             pass

#         r_p_q_e = email_check("Query replied!")
#         if r_p_q_e == True:
#             zz = SendEmailReplyQuery(
#                 request.data["ReplyTo"], app_url, user_id, user_name
#             )
#             print(f"----------email_query_replied-------{zz}")
#         else:
#             pass
#         # adding for the expert transaction table
#         start_time = query_obj.ApptId.slot_id.schedule_start_time
#         end_time = query_obj.ApptId.slot_id.schedule_end_time
#         f_time = (end_time - start_time).total_seconds()
#         f_time = f_time / 1800
#         transaction_amount = (
#             f_time * query_obj.ApptId.slot_id.doctor.doctordetails.ConsultationFees
#         )
#         wallet_obj = ExpertWalletTransactions.objects.filter(
#             WalletId__ExpertId__exact=query_obj.ApptId.slot_id.doctor.id
#         )
#         wallet_exp = ExpertWallet.objects.filter(
#             ExpertId_id__exact=query_obj.ApptId.slot_id.doctor.id
#         )[0]
#         if wallet_obj.exists():
#             wallet_obj = wallet_obj.order_by("-TransactionDate")[0]
#             balance = wallet_obj.BalanceAmount + transaction_amount
#             wallet_row = ExpertWalletTransactions.objects.create(
#                 BalanceAmount=balance,
#                 WalletId_id=wallet_exp.id,
#                 TransactionType=0,
#                 TransactionAmount=transaction_amount,
#             )
#         else:
#             balance = transaction_amount
#             wallet_row = ExpertWalletTransactions.objects.create(
#                 BalanceAmount=balance,
#                 WalletId_id=wallet_exp.id,
#                 TransactionType=0,
#                 TransactionAmount=transaction_amount,
#             )
#         return resp


class ReplyPatientQuery(generics.CreateAPIView):
    serializer_class = PatientQueriesSerializer

    def post(self, request, *args, **kwargs):
        try:
            reply_to_id = request.data.get("ReplyTo")

            # Check if already replied
            if PatientQueries.objects.filter(ReplyTo=reply_to_id).exists():
                return Response("Patient query has already been replied.")

            query_obj = PatientQueries.objects.get(id=reply_to_id)
            appointment = query_obj.ApptId
            slot = appointment.slot_id
            doctor = slot.doctor

            # Payment
            try:
                payment = PatientPayment.objects.get(
                    AppointmentId=appointment.id)
                transaction_amount = int(payment.amount)
            except PatientPayment.DoesNotExist:
                return Response("No payment found for this appointment.", status=400)

            commission_percentage = getattr(
                getattr(doctor, "doctordetails", None),
                "CommissionPercentage",
                0
            ) or 0

            commission_amount = int(
                (transaction_amount * commission_percentage) / 100)
            credit_amount = transaction_amount - commission_amount

            # Wallet
            wallet_exp, _ = ExpertWallet.objects.get_or_create(ExpertId=doctor)

            last_transaction = ExpertWalletTransactions.objects.filter(
                WalletId=wallet_exp
            ).order_by("-TransactionDate").first()

            previous_balance = last_transaction.BalanceAmount if last_transaction else 0
            new_balance = previous_balance + credit_amount

            ExpertWalletTransactions.objects.create(
                WalletId=wallet_exp,
                TransactionType=0,  # credit
                TransactionAmount=credit_amount,
                CommissionAmount=commission_amount,
                BalanceAmount=new_balance,
                appointment=appointment
                # PaymentStatus=0,  # pending
            )

            print(
                f"Credited {credit_amount} to wallet of doctor ID {doctor.id}")
            # Save reply
            resp = self.create(request, *args, **kwargs)

            # Notifications
            user = appointment.patient
            app_url = os.getenv(
                "NEXT_CANCER_UNWIRED_PATIENT_APP", "") + "/myprofile?tab=inbox"

            if notification_check("Query replied!"):
                SendPushReplyQuery(reply_to_id, app_url, user.id, user.name)

            if email_check("Query replied!"):
                SendEmailReplyQuery(reply_to_id, app_url, user.id, user.name)

            return resp

        except Exception as e:
            print("UNEXPECTED ERROR in ReplyPatientQuery:")
            return Response({"error": str(e)}, status=500)


class ExpertBlogView(generics.ListCreateAPIView):
    serializer_class = ExpertBlogsSerializer

    def create(self, request, *args, **kwargs):
        try:
            b_images1 = []
            b_subimages = []
            i = 0
            print(f"length---{len(request.FILES)}")

            while i < len(request.FILES):
                try:
                    if f"BlogImages[{i}]" in request.data:
                        b1 = handle_uploaded_file(
                            request.data[f"BlogImages[{i}]"])
                        f_name = b1.split("/")[2]
                        file_url = settings.PROJECT_ROOT + b1
                        file_urls_res = handle_s3_uploaded_file(
                            f_name, file_url)
                        print(f"urlsssssssssss{file_urls_res}")
                        b_images1.append(file_urls_res)

                    if f"BlogSubImage[{i}]" in request.data:
                        b1 = handle_uploaded_file(
                            request.data[f"BlogSubImage[{i}]"])
                        f_name = b1.split("/")[2]
                        file_url = settings.PROJECT_ROOT + b1
                        file_urls_res = handle_s3_uploaded_file(
                            f_name, file_url)
                        print(f"urlsssssssssss{file_urls_res}")
                        b_subimages.append(file_urls_res)

                except Exception as e:
                    logger.error(f"Error processing images: {str(e)}")

                i += 1

            try:
                if "BlogBannerImage" in request.data:
                    bb1 = handle_uploaded_file(request.data["BlogBannerImage"])
                    ff_name = bb1.split("/")[2]
                    file_url_b = settings.PROJECT_ROOT + bb1
                    file_urls_res_b = handle_s3_uploaded_file(
                        ff_name, file_url_b)
                    request.data["BlogBannerImage"] = file_urls_res_b

                if "BlogFeatureImage" in request.data:
                    bbf1 = handle_uploaded_file(
                        request.data["BlogFeatureImage"])
                    fff_name = bbf1.split("/")[2]
                    file_url_f = settings.PROJECT_ROOT + bbf1
                    file_urls_res_f = handle_s3_uploaded_file(
                        fff_name, file_url_f)
                    request.data["BlogFeatureImage"] = file_urls_res_f

            except Exception as e:
                logger.error(f"Error handling banner/feature images: {str(e)}")

            request.data["BlogSubImage"] = json.dumps(b_subimages)
            request.data["BlogImages"] = json.dumps(b_images1)
            request.data["ExpertId"] = self.kwargs["expert_id"]

            # ---------admin creating the blogs-----------------
            if get_cu_user_type(self.kwargs["expert_id"]) in ["admin", "child_admin"]:
                request.data["BlogStatus"] = 1

            # Create blog
            blog_response = super().create(request, *args, **kwargs)

            # ----------- Send Notification to Admin ------------
            try:
                noti_check = notification_check("Blog approval!")
                if noti_check:
                    NotifyAdminForApproval(
                        "Blog submission!", self.kwargs["expert_id"], "blogs")
            except Exception as e:
                logger.error(f"Push notification failed: {str(e)}")

            return blog_response

        except Exception as e:
            logger.error(f"Unexpected error in create method: {str(e)}")
            return Response({"error": "An unexpected error occurred"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def get_queryset(self):
        id_val = self.kwargs.get("expert_id")
        status_val = self.kwargs.get("blog_status")
        query_list = ExpertBlogs.objects.filter(BlogStatus__in=[0, 1, 2])
        if id_val != "all":
            if status_val != "all":
                query_list = query_list.filter(
                    ExpertId__exact=int(id_val), BlogStatus__exact=int(status_val)
                )
            else:
                query_list = query_list.filter(ExpertId__exact=int(id_val))
        else:
            if status_val != "all":
                query_list = query_list.filter(
                    BlogStatus__exact=int(status_val))
            else:
                pass

        for x in self.request.GET:
            if x == "Category":
                query_list = query_list.filter(
                    BlogCategory__exact=self.request.GET["Category"]
                )
            elif x == "search":
                query_list = query_list.filter(
                    Q(BlogCategory__exact=self.request.GET["search"])
                    | Q(BlogTitle__icontains=self.request.GET["search"])
                )
            elif x == "start_date":
                start_date = timezone.make_aware(
                    parse_datetime(self.request.GET["start_date"]),
                    timezone.get_current_timezone(),
                )
                query_list = query_list.filter(BlogDateTime__gte=start_date)
            elif x == "end_date":
                end_date = timezone.make_aware(
                    parse_datetime(self.request.GET["end_date"]),
                    timezone.get_current_timezone(),
                )
                query_list = query_list.filter(BlogDateTime__lte=end_date)
            elif x == "Role":
                query_list = query_list.filter(
                    ExpertId__groups__name__exact=self.request.GET["Role"]
                )
            elif x == "exp":
                query_list = query_list.filter(
                    ExpertId__doctordetails__Experience__exact=self.request.GET["exp"]
                )
            elif x == "location":
                query_list = query_list.filter(
                    Q(
                        ExpertId__doctordetails__City__icontains=self.request.GET[
                            "location"
                        ]
                    )
                    | Q(
                        ExpertId__doctordetails__Country__iexact=self.request.GET[
                            "location"
                        ]
                    )
                )
            else:
                print("Not a valid filter")

        print(f"queriesss------{query_list}------{len(query_list)}")
        return query_list

    def get(self, request, *args, **kwargs):
        b = self.list(request, *args, **kwargs)
        # print(f"-----------{b.data}")

        y = json.loads(json.dumps(b.data))
        dataa = []
        if "page" in request.GET and request.GET["page"] != "":
            # Get the page number from the request
            page_number = request.GET.get("page", 1)
            # Get the number of items per page from the request
            items_per_page = request.GET.get("per_page", 10)
            total_items = len(y)
            paginator = Paginator(y, items_per_page)
            if int(page_number) not in range(1, int(paginator.num_pages) + 1):
                return HttpResponse("Not a valid page number", status=400)
            y = paginator.page(page_number)
        for x in y:
            if x["BlogImages"] is not None:
                imagess = x["BlogImages"]
                b_images1 = []
                c = 0
                for i in imagess:
                    new_obj_url = get_s3_signed_url_bykey(i)
                    b_images1.append(new_obj_url)
                x["BlogImages"] = b_images1
            if x["BlogSubImage"] is not None:
                imagess = x["BlogSubImage"]
                b_subimages = []
                c = 0
                for i in imagess:
                    new_obj_url = get_s3_signed_url_bykey(i)
                    b_subimages.append(new_obj_url)
                x["BlogSubImage"] = b_subimages
            if x["BlogBannerImage"] is not None:
                new_obj_url = get_s3_signed_url_bykey(x["BlogBannerImage"])
                x["BlogBannerImage"] = new_obj_url
            if x["BlogFeatureImage"] is not None:
                new_obj_url = get_s3_signed_url_bykey(x["BlogFeatureImage"])
                x["BlogFeatureImage"] = new_obj_url
            if x["BlogStatus"] == 2:
                rr = StatusReason.objects.filter(
                    ExpertId_id__exact=x["ExpertId"],
                    ReasonCategory__exact=f"{x['id']}_expert_blog_rejection",
                    ReasonType__exact="Blog_Rejection",
                ).order_by("-CurrentTime")
                if rr.exists():
                    reason = StatusReason.objects.filter(
                        ExpertId_id__exact=x["ExpertId"],
                        ReasonCategory__exact=f"{x['id']}_expert_blog_rejection",
                        ReasonType__exact="Blog_Rejection",
                    ).order_by("-CurrentTime")[0]
                    x["BlogStatus_Reason"] = reason.Reason
            if x["BlogSectionVal"] is not None:
                x["BlogSectionName"] = BlogSection.objects.get(
                    id__exact=x["BlogSectionVal"]
                ).SectionName
            if x["BlogCategoryVal"] is not None:
                x["BlogCategoryVal"] = BlogCategory.objects.get(
                    id__exact=x["BlogCategoryVal"]
                ).Category
            dict1 = dict()
            # ----------------added for the check of admin/experts----------------------------------------
            user_data = []
            if get_cu_user_type(x["ExpertId"]) in ["admin", "child_admin"]:
                user_data = {
                    "expert_details": {
                        "id": x["ExpertId"],
                        "prefix": CuUser.objects.get(id=x["ExpertId"]).prefix,
                        "name": "Cancer Unwired Team",
                        "role": get_cu_user_type(x["ExpertId"]),
                        "doctor_other_details": {
                            "ProfilePhoto": get_s3_signed_url_bykey(
                                "2024-08-06-11-28-28-MQ9HIZABRB_logo_OLIWZYL.png"
                            )
                        },
                    }
                }
            elif get_cu_user_type(x["ExpertId"]) in [
                "doctor",
                "researcher",
                "influencer",
            ]:
                user_data = filter_user_data(
                    get_user_model().objects.get(id__exact=x["ExpertId"])
                )
                print(f"other------{user_data['doctor_other_details']}")

                if user_data["doctor_other_details"]["ProfilePhoto"] is not None:
                    new_obj_url = get_s3_signed_url_bykey(
                        user_data["doctor_other_details"]["ProfilePhoto"]
                    )
                    user_data["doctor_other_details"]["ProfilePhoto"] = new_obj_url
            else:
                pass

            # if user_data['doctor_other_details']['Signature'] is not None:
            #         new_obj_url = get_s3_signed_url_bykey(user_data['doctor_other_details']['Signature'])
            #         user_data['doctor_other_details']['Signature']=new_obj_url
            #         print(f"ddd details--------{new_obj_url}")

            # if user_data['doctor_other_details']['IntVideoUrl'] is not None:
            #     a_i=[]
            #     b=user_data['doctor_other_details']['IntVideoUrl']
            #     for i in b:
            #         new_obj_url = get_s3_signed_url_bykey(i)
            #         a_i.append(new_obj_url)
            #         print(f"ddd details video--------{new_obj_url}")
            #     user_data['doctor_other_details']['IntVideoUrl']=a_i

            # #added
            # if user_data['doctor_other_details']['IntroVideoStatus']==3:
            #     dd=StatusReason.objects.filter(ExpertId_id__exact=x['ExpertId'],ReasonType__exact="Intro_Video_Rejection")
            #     if dd.exists():
            #         reason = StatusReason.objects.filter(ExpertId_id__exact=x['ExpertId'],ReasonType__exact="Intro_Video_Rejection").order_by('-CurrentTime')[0]
            #         user_data['doctor_other_details']['IntroVideoStatus_Reason']=reason.Reason
            # added
            dict1["blog_details"] = x
            dict1["blog_details"]["expert_details"] = user_data
            dataa.append(dict1)
        if "page" in request.GET and request.GET["page"] != "":
            response_data = {
                "total_items": total_items,
                "total_pages": paginator.num_pages,
                "items": dataa,
            }
            return JsonResponse(response_data)
        else:
            return JsonResponse(dataa, safe=False)

    def post(self, request, *args, **kwargs):
        if get_cu_user_type(self.kwargs["expert_id"]) in [
            "doctor",
            "researcher",
            "influencer",
            "admin",
            "child_admin",
        ]:
            a = self.create(request, *args, **kwargs)

            return a
        else:
            return JsonResponse({"message": "Only experts and admin can upload blogs"})


def EditIntVideo(video_url, video_effect, video_length, soundtrack):

    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json",
        "x-api-key": "8JpuwBWmguFQLShy6IiDN0Yi7WGk6Tcxb4t8Xfl5",
    }
    filename = (
        timezone.now().strftime("%Y-%m-%d-%H-%M-%S")
        + "-"
        + "".join(
            random.choice("ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789") for i in range(10)
        )
        + "_video"
    )
    prefix = "testing"
    json1 = {
        "timeline": {
            "tracks": [
                {
                    "clips": [
                        {
                            "asset": {
                                "type": "video",
                                "src": video_url,
                            },
                            "start": 0.0,
                            "length": video_length,
                            "filter": video_effect,
                        }
                    ]
                }
            ],
        },
        "output": {
            "format": "mp4",
            "resolution": "sd",
            "destinations": [
                {
                    "provider": "s3",
                    "options": {
                        "region": "ap-south-1",
                        "bucket": "cu-files",
                        "prefix": prefix,
                        "filename": filename,
                    },
                },
                {"provider": "shotstack", "exclude": True},
            ],
        },
    }
    if soundtrack is not None:
        json1["timeline"].update(
            {"soundtrack": {"src": soundtrack, "effect": "fadeInFadeOut"}}
        )
    print(f"jsonnnnnnnnnnnnnnn{json1}")
    r = requests.post(
        "https://api.shotstack.io/edit/v1/render", json=json1, headers=headers
    )
    a = r.json()
    print(f"responsssssssssssssssss{a}----{type(a)}")
    if a["message"] == "Created":
        return a["response"]["id"]
    else:
        return False


def apply_video_effect(video, effect):
    if effect == "grayscale":
        return video.fx(vfx.blackwhite)
    elif effect == "fadein":
        return video.fx(vfx.fadein, 2)
    # Add more effects as needed
    return video


def upload_to_s3(file_obj, suffix):
    key = f"uploads/{uuid.uuid4()}{suffix}"
    s3 = boto3.client(
        's3',
        aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID"),
        aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY"),
        region_name=os.getenv("AWS_REGION"),
    )
    s3.upload_fileobj(file_obj, os.getenv("AWS_S3_BUCKET"), key)
    return key


def download_from_s3(s3_key, suffix):
    s3 = boto3.client(
        's3',
        aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID"),
        aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY"),
        region_name=os.getenv("AWS_REGION"),
    )
    temp = tempfile.NamedTemporaryFile(delete=False, suffix=suffix)
    s3.download_fileobj(os.getenv("AWS_S3_BUCKET"), s3_key, temp)
    temp.close()
    return temp.name


def merge_audio_video(video_path, audio_path, output_path):
    """
    Merges an audio file with a video file using moviepy.

    Args:
        video_path (str): Path to the video file.
        audio_path (str): Path to the audio file.
        output_path (str): Path to save the merged video file.
    """
    video_clip = VideoFileClip(video_path)
    audio_clip = AudioFileClip(audio_path)

    # Ensure audio clip is not longer than video clip
    if audio_clip.duration > video_clip.duration:
        audio_clip = audio_clip.subclip(0, video_clip.duration)

    final_clip = video_clip.set_audio(audio_clip)
    final_clip.write_videofile(output_path, codec='libx264', audio_codec='aac')
    video_clip.close()
    audio_clip.close()
    final_clip.close()


def GetEditedUrl(video_file, audio_file=None):
    if not video_file:
        return Response({"error": "Video file is required."}, status=status.HTTP_400_BAD_REQUEST)

    try:
        # Upload video and audio files to S3
        video_key = upload_to_s3(video_file, '.mp4')
        audio_key = upload_to_s3(audio_file, '.mp3') if audio_file else None

        # Download for processing
        video_path = download_from_s3(video_key, '.mp4')
        output_path = tempfile.NamedTemporaryFile(
            delete=False, suffix='.mp4').name

        if audio_key:
            audio_path = download_from_s3(audio_key, '.mp3')
            try:
                merge_audio_video(video_path, audio_path, output_path)
            except Exception as e:
                os.remove(video_path)
                os.remove(audio_path)
                os.remove(output_path)
                return Response({"error": f"MoviePy error: {e}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            finally:
                os.remove(audio_path)
        else:
            # No audio: just use video
            output_path = video_path

        # Upload merged video to S3
        s3 = boto3.client(
            's3',
            aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID"),
            aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY"),
            region_name=os.getenv("AWS_REGION"),
        )

        final_key = f"intro_videos_{uuid.uuid4()}.mp4"
        s3.upload_file(output_path, os.getenv("AWS_S3_BUCKET"), final_key)

        # Cleanup
        if output_path != video_path:
            os.remove(output_path)
        os.remove(video_path)

        return final_key

    except Exception as e:
        print("Unexpected error:", e)
        return "rendering"


def ReduceBGNoise(file_url):
    url1 = "http://localhost:8001/reduce_bg_noise/"
    try:
        res1 = requests.request(
            "POST", url1, data=json.dumps(dict({"video_file": file_url}))
        )
        print(f"in try response fileeeeeeee{res1}")

    except Exception as e:
        print(f"in exceeeeeeee{e}")
    exit()


class UploadIntroVideoView(generics.UpdateAPIView):
    queryset = DoctorDetails.objects.all()
    lookup_field = "DoctorId"
    serializer_class = DoctorDetailsSerializer

    def partial_update(self, request, *args, **kwargs):
        file_path = (
            "http://"
            + request.META["HTTP_HOST"]
            + handle_uploaded_file(request.FILES["video_url"])
        )
        # file_path = "https://" + request.META['HTTP_HOST'] + h1
        file_path1 = None
        h2 = None
        if "audio_url" in request.data:
            file_path1 = new_obj_url = get_s3_signed_url_bykey(
                request.data["audio_url"]
            )
        print(f"pathhhhhhhhhhh{file_path}")
        video_id = EditIntVideo(
            file_path,
            request.data["video_effect"],
            float(request.data["video_length"]),
            file_path1,
        )

        if video_id:

            print(f"int video1--------{video_id}--------{type(video_id)}")
            instance = self.get_object()
            # modified for new IntroVideo feature while old_video displays until new video is approved
            a = []
            if instance.IntVideoUrl == None:
                a.append(str(video_id))
                instance.IntVideoUrl = a
            elif len(instance.IntVideoUrl) == 1 and instance.IntroVideoStatus == 2:
                a.append(instance.IntVideoUrl[0])
                a.append(str(video_id))
                instance.IntVideoUrl = a
            elif len(instance.IntVideoUrl) == 1 and instance.IntroVideoStatus == 3:
                a.append(str(video_id))
                instance.IntVideoUrl = a
            elif len(instance.IntVideoUrl) == 2 and instance.IntroVideoStatus == 3:
                a.append(instance.IntVideoUrl[0])
                a.append(str(video_id))
                instance.IntVideoUrl = a
            else:
                pass
            # -----------------------------------------------------------------------------------------
            # instance.IntVideoUrl = file_path
            instance.save(update_fields=["IntVideoUrl"])
            if h2 is not None:
                print(f"in audio fileeee")
                os.remove(settings.PROJECT_ROOT + h2)
            return super().partial_update(request, *args, **kwargs)
        else:
            print(f"int video2--------{video_id}--------{type(video_id)}")
            if h2 is not None:
                print(f"in audio fileeee elseee")
                os.remove(settings.PROJECT_ROOT + h2)
            return super().partial_update(request, *args, **kwargs)

    # def put(self, request, *args, **kwargs):

    #     a = self.partial_update(request, *args, **kwargs)

    #     if a.status_code == 200:

    #         return Response({"id": a.data, "upload_status": "upload status success"})
    #     else:
    #         return Response("upload status failed")
    def put(self, request, *args, **kwargs):
        try:
            response = self.partial_update(request, *args, **kwargs)

            if response.status_code == 200:
                # Getting DoctorId from URL
                doctor_id = self.kwargs.get("DoctorId")

                # Sending Notification to Admin
                noti_check = notification_check("Introduction video approval!")
                print("noti_check", noti_check)

                if noti_check:
                    try:
                        noti = NotifyAdminForApproval(
                            "Intro video submission!",
                            doctor_id
                        )
                        print("notification in the introduction video ", noti_check)
                    except Exception as e:
                        logger.error(f"Push notification failed: {str(e)}")

                return Response(
                    {"id": response.data, "upload_status": "upload status success"}
                )

            else:
                return Response(
                    {"error": "upload status failed"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

        except Exception as e:
            logger.error(f"Error in put method: {str(e)}")
            return Response(
                {"error": "An unexpected error occurred"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class SubmitIntroVideoView(generics.UpdateAPIView):
    queryset = DoctorDetails.objects.all()
    lookup_field = "DoctorId"
    serializer_class = DoctorDetailsSerializer

    def partial_update(self, request, *args, **kwargs):

        audio_file = None
        if "audio_url" in request.data:
            audio_file = request.data["audio_url"]
        else:
            audio_file = None
        print(request.data['intro_video'])
        edited_url = GetEditedUrl(request.data["intro_video"], audio_file)

        print(f"edited_urllllllllllll------{edited_url}")
        if edited_url != "rendering":
            instance = self.get_object()
            a = []
            if instance.IntVideoUrl == None or instance.IntVideoUrl == []:
                a.append(edited_url)
                print("aaaaaaaaaaaaaaaaaaaaaaaaaa", a)
            elif len(instance.IntVideoUrl) == 1 and instance.IntroVideoStatus == 2:
                a.append(instance.IntVideoUrl[0])
                a.append(str(edited_url))
                print("aaaaaaaaaaaaaaaaaaaaaaaaaa", a)

            elif len(instance.IntVideoUrl) == 2 and instance.IntroVideoStatus == 3:
                a.append(instance.IntVideoUrl[0])
                a.append(str(edited_url))
                print("aaaaaaaaaaaaaaaaaaaaaaaaaa", a)
            else:
                pass
            instance.IntVideoUrl = a
            instance.IntroVideoStatus = 1
            instance.save(update_fields=["IntVideoUrl", "IntroVideoStatus"])

            return super().partial_update(request, *args, **kwargs)
        else:
            return edited_url

    def put(self, request, *args, **kwargs):
        a = self.partial_update(request, *args, **kwargs)

        if isinstance(a, Response):
            print(f"response---------{a}---------{type(a)}")

            # Check if notifications are enabled
            noti_check = notification_check("Introduction video approval!")

            if noti_check and a.status_code == 200:
                doctor_id = self.kwargs.get(
                    "DoctorId")  # Get DoctorId from URL
                if doctor_id:
                    try:
                        NotifyAdminForApproval(
                            "Intro video submission!", doctor_id)
                    except Exception as e:
                        logger.error(
                            f"Error sending push notification: {str(e)}")

            return a
        else:
            return Response({"message": a})


@method_decorator(csrf_exempt, name="dispatch")
class ShotStackTest(View):
    def post(self, r):

        host = "https://api.shotstack.io/stage"

        # if os.getenv("SHOTSTACK_HOST") is not None:
        #     host = os.getenv("SHOTSTACK_HOST")

        configuration = shotstack.Configuration(host=host)

        # if os.getenv("SHOTSTACK_KEY") is None:
        #     sys.exit("API Key is required. Set using: export SHOTSTACK_KEY=your_key_here")

        configuration.api_key["DeveloperKey"] = (
            "8JpuwBWmguFQLShy6IiDN0Yi7WGk6Tcxb4t8Xfl5"
        )
        with shotstack.ApiClient(configuration) as api_client:
            api_instance = edit_api.EditApi(api_client)

            filters = [
                "original",
                "boost",
                "contrast",
                "muted",
                "darken",
                "lighten",
                "greyscale",
                "negative",
            ]

            video_clips = []
            title_clips = []
            start = 0.0
            length = 3.0
            trim = 0.0
            end = length

            soundtrack = Soundtrack(
                src="https://s3-ap-southeast-2.amazonaws.com/shotstack-assets/music/freeflow.mp3",
                effect="fadeInFadeOut",
            )

            for _filter in filters:
                # Video clips
                video_asset = VideoAsset(
                    src="https://s3-ap-southeast-2.amazonaws.com/shotstack-assets/footage/skater.hd.mp4",
                    trim=trim,
                )

                video_clip = Clip(asset=video_asset,
                                  start=start, length=length)

                if _filter != "original":
                    video_transition = Transition(_in="wipeRight")

                    video_clip["transition"] = video_transition
                    video_clip["filter"] = _filter
                    video_clip["length"] = length + 1

                video_clips.append(video_clip)

                # Title clips
                title_transition = Transition(_in="fade", out="fade")

                title_asset = TitleAsset(
                    text=_filter, style="minimal", size="x-small")

                title_clip = Clip(
                    asset=title_asset,
                    length=length - (1 if start == 0 else 0),
                    start=start,
                    transition=title_transition,
                )

                title_clips.append(title_clip)

                trim = end - 1
                end = trim + length + 1
                start = trim

            track_1 = Track(clips=title_clips)

            track_2 = Track(clips=video_clips)

            timeline = Timeline(
                background="#000000", soundtrack=soundtrack, tracks=[track_1, track_2]
            )

            output = Output(format="mp4", resolution="sd", fps=30.0)

            edit = Edit(timeline=timeline, output=output)

            try:
                api_response = api_instance.post_render(edit)

                message = api_response["response"]["message"]
                id = api_response["response"]["id"]

                print(f"{message}\n")
                print(">> Now check the progress of your render by running:")
                print(f">> python examples/status.py {id}")
                return Response(id)
            except Exception as e:
                print(f"Unable to resolve API call: {e}")
                return Response(e)


class ExpertFeedbackView(generics.ListCreateAPIView):
    permission_classes = []
    serializer_class = ExpertFeedbackSerializer

    def get_queryset(self):

        if self.kwargs["status"] in ["1", "0", "2"]:
            # -------------added for admin all approvals ---------------------------------------------------------------------------------
            if self.kwargs["expert_id"] == "all":
                data = ExpertFeedback.objects.filter(status__exact=2)
            else:
                data = ExpertFeedback.objects.filter(
                    ExpertId__exact=self.kwargs["expert_id"],
                    status__exact=int(self.kwargs["status"]),
                )
        # ----------------------------------------------------------------------------------------------------------------------------
        else:
            data = ExpertFeedback.objects.filter(
                ExpertId__exact=self.kwargs["expert_id"]
            )

        return data

    def get(self, request, *args, **kwargs):

        res = self.list(request, *args, **kwargs)
        # return res
        y = json.loads(json.dumps(res.data))
        if self.kwargs["expert_id"] == "all":
            # Get the page number from the request
            page_number = request.GET.get("page", 1)
            # Get the number of items per page from the request
            items_per_page = request.GET.get("per_page", 10)
            total_items = len(y)
            paginator = Paginator(y, items_per_page)
            if int(page_number) not in range(1, int(paginator.num_pages) + 1):
                return HttpResponse("Not a valid page number", status=400)
            y = paginator.page(page_number)
            exp_f = []
            for x in y:
                doctor_name = DoctorDetails.objects.get(
                    DoctorId__exact=x["ExpertId"]
                ).DoctorId.name
                doctor_photo = DoctorDetails.objects.get(
                    DoctorId__exact=x["ExpertId"]
                ).ProfilePhoto
                if doctor_photo is not None:
                    print(f"doctor Photo------------{doctor_photo}")
                    new_obj_url = get_s3_signed_url_bykey(doctor_photo)
                    doctor_photo = new_obj_url
                x["doctor_name"] = doctor_name
                x["doctor_photo"] = doctor_photo
                x["expert_role"] = get_cu_user_type(x["ExpertId"])
                exp_f.append(x)
            return JsonResponse(
                {
                    "total_items": total_items,
                    "total_pages": paginator.num_pages,
                    "items": exp_f,
                }
            )
        else:
            for x in y:
                doctor_name = DoctorDetails.objects.get(
                    DoctorId__exact=x["ExpertId"]
                ).DoctorId.name
                doctor_photo = DoctorDetails.objects.get(
                    DoctorId__exact=x["ExpertId"]
                ).ProfilePhoto
                if doctor_photo is not None:
                    print(f"doctor Photo------------{doctor_photo}")
                    new_obj_url = get_s3_signed_url_bykey(doctor_photo)
                    doctor_photo = new_obj_url
                x["doctor_name"] = doctor_name
                x["doctor_photo"] = doctor_photo
                x["expert_role"] = get_cu_user_type(x["ExpertId"])
            return Response(y)

    def post(self, r, *args, **kwargs):
        try:
            a = self.create(r, *args, **kwargs)

            expertId = r.data.get("ExpertId")

            if not expertId:
                return Response({"error": "ExpertId is required"}, status=status.HTTP_400_BAD_REQUEST)

            noti_check = notification_check("Feedback approval!")

            if noti_check:
                try:
                    NotifyAdminForApproval(
                        "Feedback submission!", expertId, "feedback")
                except Exception as e:
                    logger.error(f"Push notification failed: {str(e)}")

            return a  # ✅ Return success response

        except Exception as e:
            logger.error(f"Unexpected error in post method: {str(e)}")
            return Response({"error": "An unexpected error occurred"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class UpdateExpertFeedbackView(generics.RetrieveUpdateDestroyAPIView):
    serializer_class = ExpertFeedbackSerializer
    queryset = ExpertFeedback.objects.all()
    lookup_field = "id"

    def put(self, r, *args, **kwargs):
        try:
            # Perform the partial update
            a = self.partial_update(r, *args, **kwargs)

            # Check if 'status' exists in request data
            if "status" in r.data:
                try:
                    expert_id = ExpertFeedback.objects.get(
                        id=self.kwargs["id"])
                except ExpertFeedback.DoesNotExist:
                    return Response({"error": "Expert Feedback not found"}, status=status.HTTP_404_NOT_FOUND)

                noti_check = notification_check("Feedback approval!")

                # Safely get user_id from GET parameters
                user_id = r.GET.get("user_id", None)

                if not user_id:
                    return Response({"error": "User ID is required"}, status=status.HTTP_400_BAD_REQUEST)

                if noti_check:
                    try:
                        AdminApprovePushNotification(
                            "Feedback approval!",
                            expert_id.ExpertId_id,
                            r.data["status"],
                            user_id,
                            "N",
                        )
                    except Exception as e:
                        print(f"Error sending push notification: {e}")

                e_check = email_check("Feedback approval!")

                if e_check:
                    try:
                        AdminApprovePushNotification(
                            "Feedback approval!",
                            expert_id.ExpertId_id,
                            r.data["status"],
                            user_id,
                            "E",
                        )
                    except Exception as e:
                        print(f"Error sending email notification: {e}")

            return a  # Return the response after successful update

        except Exception as e:
            print(f"Unexpected error in put method: {e}")
            return Response({"error": "An unexpected error occurred"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


def cu_sendreviewlink(u_email, code, email_sub, host, doctor_id):

    print(f"review send mail---------")

    # zeptomail
    # r_link = host+"/reviewdoctor/?verify_code="+code+"&doctor_id="+str(doctor_id)
    email_list = []
    for x in u_email:
        a_e = x["userName"]
        r_link = (
            host
            + "/reviewdoctor/?verify_code="
            + code
            + "&doctor_id="
            + str(doctor_id)
            + "&p_email="
            + str(x["userEmail"])
        )
        if x["userName"] in ["No Name", "No name"]:
            x["userName"] = "there"
            a_e = x["userEmail"]
        else:
            x["userName"] = x["userName"].capitalize()
            print(x["userName"])
        a = {
            "email_address": {"address": x["userEmail"], "name": a_e},
            "merge_info": {"r_link": r_link,
                           "p_name": x["userName"],
                           "fb_url": os.getenv("fb_url"),
                           "insta_url": os.getenv("insta_url"),
                           "twitter_url": os.getenv("twitter_url"),
                           "linkedin_url": os.getenv("linkedin_url"),
                           "youtube_url": os.getenv("youtube_url"), },
        }
        email_list.append(a)

    payload = {
        "template_key": "2518b.41c485fda42f6e5f.k1.6373ef90-f72f-11ee-b957-52540038fbba.18ec7d0e509",
        # "bounce_address": "<EMAIL>",
        "from": {"address": "<EMAIL>", "name": "Health Unwired"},
        "to": email_list,
    }

    headers = {
        "Authorization": f'{os.getenv("ZEPTOMAIL_TOKEN")}',
        "Content-Type": "application/json",
        "Accept": "application/json",
    }
    zeptomail_url = "https://api.zeptomail.in/v1.1/email/template/batch"

    try:
        response = requests.request(
            "POST", zeptomail_url, headers=headers, data=json.dumps(payload)
        )
        print(
            f"ask reviewwwwwwww mail send-----------{response.status_code}-------{response.content}--------"
        )
        response_status = (
            True
            if response.status_code == 200
            else True if response.status_code in [201, 202] else False
        )
        return response_status
    except HTTPError as e:
        print(f"exception-----------{e.to_dict}")
        return response_status
    # zeptomail ends


# added
class ExpertReviewView(generics.ListCreateAPIView):
    serializer_class = DoctorReviewsSerializer
    queryset = DoctorReviews.objects.all()

    def get_queryset(self):
        a = DoctorReviews.objects.filter(
            ReviewLinkStatus__exact=2,
            ReviewStatus__in=["1", "2", "3"],
            ExpertId__exact=self.request.GET["expert_id"],
        )

        review_status = self.request.GET.get('status')
        if review_status:
            a = a.filter(ReviewStatus=review_status)
        return a

    def create(self, r, *args, **kwargs):
        r.data["ReviewCode"] = "".join(
            random.choice(
                "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
            )
            for i in range(16)
        )
        return super().create(r, *args, **kwargs)

    def post(self, r, *args, **kwargs):
        a = self.create(r, *args, **kwargs)

        data = json.loads(json.dumps(a.data))
        print(
            f"in reviews creation----------{a.status_code}--------------{data}")
        if a.status_code in [200, 201]:
            # added
            sent_status = ""
            e_rl = email_check("Send review_link email")
            if e_rl == True:
                sent_status = cu_sendreviewlink(
                    data["PatientEmail"],
                    data["ReviewCode"],
                    "Ask for review",
                    os.getenv("NEXT_CANCER_UNWIRED_PATIENT_APP"),
                    r.data["ExpertId"],
                )
            if sent_status != "":  # added
                return Response({"message": "Review link sent successfully"})
            else:
                return Response({"message": "Review link could'nt be sent "})

    def get(self, r, *args, **kwargs):
        res2 = self.list(r, *args, **kwargs)
        for x in res2.data:
            if x["ReviewStatus"] == 3:
                dd = StatusReason.objects.filter(
                    ExpertId_id__exact=x["ExpertId"],
                    ReasonCategory__exact=f"{x['id']}_review_rejection",
                    ReasonType__exact="Review_Rejection",
                )
                if dd.exists():
                    x["review_rejection_reason"] = dd[0].Reason
        return res2


class GetExpertReviewView(generics.ListAPIView):
    serializer_class = DoctorReviewsSerializer
    queryset = DoctorReviews.objects.all()
    permission_classes = []

    def get_queryset(self):
        if self.kwargs["id"] != "all":
            return DoctorReviews.objects.filter(
                ReviewLinkStatus__exact=2,
                ReviewStatus=2,
                ExpertId__exact=self.kwargs["id"],
            )
        elif self.kwargs["id"] == "all":
            return DoctorReviews.objects.filter(
                ReviewLinkStatus__exact=2, ReviewStatus=2
            )
        else:
            return []

    def get(self, r, *args, **kwargs):
        res2 = self.list(r, *args, **kwargs)
        return res2


def verify_link(gen_time, userList, email):
    is_user_valid = any(
        email.lower() == user['userEmail'].lower() for user in userList)
    if not is_user_valid:
        print("User email not found in the list.")
        return False

    expiry_time = timezone.localtime(
        gen_time, timezone.get_current_timezone()
    ) + timedelta(minutes=60)

    is_expired = (
        timezone.localtime(
            timezone.now(), timezone.get_current_timezone()).timestamp()
        >= expiry_time.timestamp()
    )
    return not is_expired


class SubmitReviewView(generics.CreateAPIView):
    permission_classes = []
    serializer_class = DoctorReviewsSerializer
    queryset = DoctorReviews.objects.all()
    lookup_field = "ReviewCode"

    def create(self, r, *args, **kwargs):
        instance = self.get_object()
        print("instance.ReviewGenTime", instance.ReviewGenTime)
        linkstatus = verify_link(
            instance.ReviewGenTime, instance.PatientEmail, r.data["PatientEmail"]
        )
        r_c = "review_" + instance.ReviewCode
        review_given = DoctorReviews.objects.filter(
            PatientEmail=r.data["PatientEmail"],
            ExpertId_id=instance.ExpertId_id,
            ReviewStatus__in=[1, 2],
            ReviewCode__exact=r_c,
        )

        try:
            if linkstatus:
                if review_given.exists():
                    return "Can not submit review again"
                else:
                    r.data["ReviewStatus"] = 1
                    r.data["PatientEmail"] = r.data["PatientEmail"]
                    r.data["ReviewLinkStatus"] = 2
                    r.data["ReviewRating"] = r.data["Rating"]
                    r.data["ExpertId"] = instance.ExpertId_id
                    r.data["ReviewCode"] = r_c
                    return super().create(r, *args, **kwargs)
            else:
                return "review link has expired"
        except Exception as e:
            return generate_response(message=str(e))

    def post(self, r, *args, **kwargs):
        res1 = self.create(r, *args, **kwargs)

        if isinstance(res1, Response):
            noti_check = notification_check("Review approval!")
            if noti_check:
                try:
                    expert_id = self.get_object().ExpertId_id  # Get expert ID from URL
                    NotifyAdminForApproval(
                        "External Review submission!", expert_id, "reviews")
                except Exception as e:
                    logger.error(f"Error sending push notification: {str(e)}")

            return res1
        else:
            return Response(res1)


class DoctorConsentView(views.APIView):

    def post(self, r, *args, **kwargs):
        print("inside the doctor consent form ----------------------")
        try:
            doctor_id = r.data.get("DoctorId")
            consent_content = r.data.get("ConsentContent")

            if not doctor_id or not consent_content:
                return Response({"error": "DoctorId and ConsentContent are required"}, status=status.HTTP_400_BAD_REQUEST)

            # Check if the user is an expert (doctor, researcher, influencer)
            if get_cu_user_type(doctor_id) in ["doctor", "researcher", "influencer"]:
                try:
                    # Check if the consent form already exists for the doctor
                    dc = DoctorConsent.objects.filter(
                        DoctorId__exact=doctor_id).exists()

                    if dc:
                        print(
                            f"Consent form already exists for DoctorId {doctor_id}")
                        a = DoctorConsent.objects.get(
                            DoctorId__exact=doctor_id)
                        a.ConsentContent = consent_content
                        a.Status = 3  # Marking it as pending approval
                        a.DateOfConsentForm = timezone.now()
                        a.save()
                    else:
                        print(
                            f"Consent form does not exist for DoctorId {doctor_id}. Creating new.")
                        a = DoctorConsent.objects.create(
                            DoctorId=get_user_model().objects.get(id__exact=doctor_id),
                            ConsentContent=consent_content,
                            DateOfConsentForm=timezone.now(),
                            Status=3  # Marking as pending approval
                        )

                    # Send Notification to Admin
                    print("this is above ntification ")
                    try:
                        noti_check = notification_check(
                            "Consent form approval!")
                        if noti_check:
                            notification = NotifyAdminForApproval(
                                "Consent form submission!", doctor_id, "consentForm")
                            print(
                                "response of the notification in the consent form ", notification)
                    except Exception as e:
                        logger.error(f"Push notification failed: {str(e)}")

                    return Response(DoctorConsentSerializer(a).data)

                except Exception as e:
                    logger.error(f"Error processing DoctorConsent: {str(e)}")
                    return Response({"error": "An error occurred while saving consent form"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            else:
                return Response({"error": "User has to be an expert (doctor, researcher, or influencer) to upload a consent form"}, status=status.HTTP_401_UNAUTHORIZED)

        except Exception as e:
            logger.error(f"Unexpected error in post method: {str(e)}")
            return Response({"error": "An unexpected error occurred"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class RetrieveDoctorConsentView(generics.RetrieveAPIView):
    serializer_class = DoctorConsentSerializer
    lookup_field = "DoctorId"
    queryset = DoctorConsent.objects.all()

    def get(self, r, *args, **kwargs):
        consent = self.retrieve(r, *args, **kwargs)
        if consent.data["DateOfConsentForm"] is not None:
            consent.data["DateOfConsentForm"] = timezone.localtime(
                parse_datetime(consent.data["DateOfConsentForm"]),
                timezone.get_current_timezone(),
            )
        print(consent.data)
        # added
        if consent.data["Status"] == 0:
            dd = StatusReason.objects.filter(
                ExpertId_id__exact=consent.data["DoctorId"],
                ReasonType__exact="Doctor_Consent_Rejection",
            )
            if dd.exists():
                reason = StatusReason.objects.filter(
                    ExpertId_id__exact=consent.data["DoctorId"],
                    ReasonType__exact="Doctor_Consent_Rejection",
                ).order_by("-CurrentTime")[0]
                return Response(
                    {
                        "message": "Consent form rejected by admin",
                        "reason": reason.Reason,
                        "consent_form_data": consent.data,
                    }
                )
        # added
        return consent


class GetExpertBlogView(generics.ListAPIView):
    permission_classes = []
    serializer_class = ExpertBlogsSerializer
    queryset = ExpertBlogs.objects.all()

    def get_queryset(self):
        query_list = ExpertBlogs.objects.all()
        if self.kwargs["id"] != "all":
            print(f"experttttttttttttttt")
            query_list = ExpertBlogs.objects.filter(
                BlogStatus__exact=1, ExpertId__exact=self.kwargs["id"]
            )
        elif self.kwargs["id"] == "all":
            print(f"allllllllllllllllllllllllll")
            query_list = ExpertBlogs.objects.filter(BlogStatus__exact=1)
        else:
            print(f"nothinggggggggggggggg")
            return []
        for x in self.request.GET:
            if x == "category":
                cat_obj = eval(self.request.GET["category"])
                query_list = query_list.filter(
                    BlogCategoryVal__Category__in=cat_obj)
            elif x == "date":
                date = timezone.make_aware(
                    parse_datetime(self.request.GET["date"]),
                    timezone.get_current_timezone(),
                )
                query_list = query_list.filter(BlogDateTime__exact=date)
            elif x == "search":
                query_list = query_list.filter(
                    Q(BlogCategoryVal__Category__exact=self.request.GET["search"])
                    | Q(BlogTitle__icontains=self.request.GET["search"])
                )
            elif x == "start_date":
                start_date = timezone.make_aware(
                    parse_datetime(self.request.GET["start_date"]),
                    timezone.get_current_timezone(),
                )
                query_list = query_list.filter(BlogDateTime__gte=start_date)
            elif x == "end_date":
                end_date = timezone.make_aware(
                    parse_datetime(self.request.GET["end_date"]),
                    timezone.get_current_timezone(),
                )
                query_list = query_list.filter(BlogDateTime__lte=end_date)
            elif x == "role":
                role_obj = eval(self.request.GET["role"])
                print(type(role_obj))
                query_list = query_list.filter(
                    ExpertId__groups__name__in=role_obj)
            elif x == "exp":
                query_list = query_list.filter(
                    ExpertId__doctordetails__Experience__exact=self.request.GET["exp"]
                )
            elif x == "location":
                query_list = query_list.filter(
                    Q(ExpertId__City__icontains=self.request.GET["location"])
                    | Q(ExpertId__Country__iexact=self.request.GET["location"])
                )
            else:
                print("Not a valid filter")
        return query_list.order_by("-id")

    def get(self, r, *args, **kwargs):
        res = self.list(r, *args, **kwargs)
        y = json.loads(json.dumps(res.data))
        dataa = []
        if "page" in r.GET and r.GET["page"] != "":

            # Get the page number from the request
            page_number = r.GET.get("page", 1)
            # Get the number of items per page from the request
            items_per_page = r.GET.get("per_page", 10)
            total_items = len(y)
            paginator = Paginator(y, items_per_page)
            print(paginator)
            if int(page_number) not in range(1, int(paginator.num_pages) + 1):
                return HttpResponse("Not a valid page number", status=400)
            y = paginator.page(page_number)
        for x in y:
            if x["BlogImages"] is not None:
                imagess = x["BlogImages"]
                b_images1 = []
                for i in imagess:
                    new_obj_url = get_s3_signed_url_bykey(i)
                    print(new_obj_url)
                    b_images1.append(new_obj_url)

                x["BlogImages"] = b_images1
            if x["BlogSubImage"] is not None:
                imagess = x["BlogSubImage"]
                b_images1 = []
                for i in imagess:
                    new_obj_url = get_s3_signed_url_bykey(i)
                    print(new_obj_url)
                    b_images1.append(new_obj_url)

                x["BlogSubImage"] = b_images1
            if x["BlogBannerImage"] is not None:
                new_obj_url = get_s3_signed_url_bykey(x["BlogBannerImage"])
                x["BlogBannerImage"] = new_obj_url
            if x["BlogFeatureImage"] is not None:
                new_obj_url = get_s3_signed_url_bykey(x["BlogFeatureImage"])
                x["BlogFeatureImage"] = new_obj_url
            if x["BlogSectionVal"] is not None:
                x["BlogSectionName"] = BlogSection.objects.get(
                    id__exact=x["BlogSectionVal"]
                ).SectionName
            if x["BlogCategoryVal"] is not None:
                x["BlogCategoryVal"] = BlogCategory.objects.get(
                    id__exact=x["BlogCategoryVal"]
                ).Category
            dict1 = dict()
            # ----------------added for the check of admin/experts----------------------------------------
            user_data = []
            if get_cu_user_type(x["ExpertId"]) in ["admin", "child_admin"]:
                user_data = {
                    "id": x["ExpertId"],
                    "prefix": CuUser.objects.get(id=x["ExpertId"]).prefix,
                    "name": "Health Unwired Team",
                    "role": get_cu_user_type(x["ExpertId"]),
                    "doctor_other_details": {
                        "ProfilePhoto": get_s3_signed_url_bykey("logo.png")
                    },
                }
            elif get_cu_user_type(x["ExpertId"]) in [
                "doctor",
                "researcher",
                "influencer",
            ]:
                user_data = filter_user_data(
                    get_user_model().objects.get(id__exact=x["ExpertId"])
                )
                print(f"other------{user_data['doctor_other_details']}")

                if user_data["doctor_other_details"]["ProfilePhoto"] is not None:
                    new_obj_url = get_s3_signed_url_bykey(
                        user_data["doctor_other_details"]["ProfilePhoto"]
                    )
                    user_data["doctor_other_details"]["ProfilePhoto"] = new_obj_url
            else:
                pass

            dict1["blog_details"] = x
            dict1["blog_details"]["expert_details"] = user_data
            dataa.append(dict1)
        if "page" in r.GET and r.GET["page"] != "":
            response_data = {
                "total_items": total_items,
                "total_pages": paginator.num_pages,
                "items": dataa,
            }
            return Response(response_data)
        else:
            return Response(dataa)
        # b = json.loads(json.dumps(res.data))


# added
class UpdateBlogsView(generics.RetrieveUpdateDestroyAPIView):
    serializer_class = ExpertBlogsSerializer
    queryset = ExpertBlogs.objects.all()
    lookup_field = "id"

    def partial_update(self, request, *args, **kwargs):
        b_images1 = []
        b_subimages = []
        i = 0
        instance = ExpertBlogs.objects.get(id=self.kwargs["id"])
        print(f"length---{len(request.data)}")
        print(request.data)
        while i < len(request.data):
            if f"BlogImages[{i}]" in request.data:
                print(type(request.data[f"BlogImages[{i}]"]))
                if type(request.data[f"BlogImages[{i}]"]) == str:
                    a = request.data[f"BlogImages[{i}]"]
                    a = a.split("/")[3]
                    a = a.split("?")[0]
                    b_images1.append(a)
                elif (
                    type(
                        request.data[f"BlogImages[{i}]"]) == InMemoryUploadedFile
                    or type(request.data[f"BlogImages[{i}]"]) == TemporaryUploadedFile
                ):

                    b1 = handle_uploaded_file(request.data[f"BlogImages[{i}]"])
                    f_name = b1.split("/")[2]
                    file_url = settings.PROJECT_ROOT + b1

                    file_urls_res = handle_s3_uploaded_file(f_name, file_url)
                    print(f"urlsssssssssss{file_urls_res}")
                    b_images1.append(file_urls_res)
                else:
                    pass
            if f"BlogSubImage[{i}]" in request.data:
                print(type(request.data[f"BlogSubImage[{i}]"]))
                if type(request.data[f"BlogSubImage[{i}]"]) == str:
                    a = request.data[f"BlogSubImage[{i}]"]
                    a = a.split("/")[3]
                    a = a.split("?")[0]
                    b_subimages.append(a)
                elif (
                    type(
                        request.data[f"BlogSubImage[{i}]"]) == InMemoryUploadedFile
                    or type(request.data[f"BlogSubImage[{i}]"]) == TemporaryUploadedFile
                ):

                    b1 = handle_uploaded_file(
                        request.data[f"BlogSubImage[{i}]"])
                    f_name = b1.split("/")[2]
                    file_url = settings.PROJECT_ROOT + b1

                    file_urls_res = handle_s3_uploaded_file(f_name, file_url)
                    print(f"urlsssssssssss{file_urls_res}")
                    b_subimages.append(file_urls_res)
                else:
                    pass
            i += 1
        if "BlogBannerImage" in request.data:
            if type(request.data["BlogBannerImage"]) == str:
                a = request.data["BlogBannerImage"]
                a = a.split("/")[3]
                a = a.split("?")[0]
                request.data["BlogBannerImage"] = a
            elif (
                type(request.data["BlogBannerImage"]) == InMemoryUploadedFile
                or type(request.data["BlogBannerImage"]) == TemporaryUploadedFile
            ):
                bb1 = handle_uploaded_file(request.data["BlogBannerImage"])
                ff_name = bb1.split("/")[2]
                file_url_b = settings.PROJECT_ROOT + bb1
                file_urls_res_b = handle_s3_uploaded_file(ff_name, file_url_b)
                request.data["BlogBannerImage"] = file_urls_res_b
        else:
            instance.BlogBannerImage = None
        if "BlogFeatureImage" in request.data:
            if type(request.data["BlogFeatureImage"]) == str:
                a = request.data["BlogFeatureImage"]
                a = a.split("/")[3]
                a = a.split("?")[0]
                request.data["BlogFeatureImage"] = a
            elif (
                type(request.data["BlogFeatureImage"]) == InMemoryUploadedFile
                or type(request.data["BlogFeatureImage"]) == TemporaryUploadedFile
            ):
                bbf1 = handle_uploaded_file(request.data["BlogFeatureImage"])
                fff_name = bbf1.split("/")[2]
                file_url_f = settings.PROJECT_ROOT + bbf1

                file_urls_res_f = handle_s3_uploaded_file(fff_name, file_url_f)
                request.data["BlogFeatureImage"] = file_urls_res_f
        else:
            instance.BlogFeatureImage = None
        instance.BlogImages = b_images1
        instance.BlogSubImage = b_subimages
        if "user_id" in request.GET and get_cu_user_type(request.GET["user_id"]) in [
            "child_admin",
            "admin",
        ]:
            instance.BlogStatus = 1
        else:
            instance.BlogStatus = 0
        instance.save()
        print(instance)
        return super().partial_update(request, *args, **kwargs)

    def put(self, request, *args, **kwargs):
        a = self.partial_update(request, *args, **kwargs)
        return a

    def delete(self, r, *args, **kwargs):
        b_id = ExpertBlogs.objects.filter(id__exact=self.kwargs["id"])
        if b_id.exists():
            b_id = ExpertBlogs.objects.get(id__exact=self.kwargs["id"])
            a = ContentRemoval(b_id, "expert_blogs")
            return Response(a)
        else:
            return Response("Item doesn't exist", status=404)


# added


# added
def send_deactivated_email(u_email, u_name, host=None, deleted=False, deleted_reason=None):
    print("------------------------------", u_email, u_name, deleted_reason)
    verify_url1 = host + "/reactivateProfile"
    if not deleted:
        payload = {
            "template_key": "2518b.41c485fda42f6e5f.k1.398bae80-1358-11ef-b3e8-525400674725.18f805cb968",
            # "bounce_address": "<EMAIL>",
            "from": {"address": "<EMAIL>", "name": "Health Unwired"},
            "to": [
                {
                    "email_address": {
                        "address": u_email,
                        "name": u_email,
                    }
                }
            ],
            "merge_info": {
                "u_name": u_name,
                "reactivation_link": verify_url1,
            },
        }

        headers = {
            "Authorization": f'{os.getenv("ZEPTOMAIL_TOKEN")}',
            "Content-Type": "application/json",
            "Accept": "application/json",
        }
        zeptomail_url = "https://api.zeptomail.in/v1.1/email/template"
        try:
            response = requests.request(
                "POST", zeptomail_url, headers=headers, data=json.dumps(payload)
            )
            print(
                f"pw mail send-----------{response.status_code}-------{response.content}--------"
            )
            response_status = (
                True
                if response.status_code == 200
                else True if response.status_code in [201, 202] else False
            )
            print(f"--------email_send_done-------{response_status}")
        except HTTPError as e:
            print(f"exception-----------{e.to_dict}")
            print(f"--------email_send_failed-------{response_status}")
        # added
        u_email = CuUser.objects.filter(groups__id__exact=3)
        email_list = []
        link_a = os.getenv("NEXT_CANCER_UNWIRED_ADMIN_APP") + "/usermanagement"
        for x in u_email:
            a = {
                "email_address": {"address": x.email, "name": x.name},
                "merge_info": {"link": link_a,
                               "name": x.name,
                               "u_name": u_name,
                               "fb_url": os.getenv("fb_url"),
                               "insta_url": os.getenv("insta_url"),
                               "twitter_url": os.getenv("twitter_url"),
                               "linkedin_url": os.getenv("linkedin_url"),
                               "youtube_url": os.getenv("youtube_url"), },
            }
            email_list.append(a)

        payload2 = {
            "template_key": "2518b.41c485fda42f6e5f.k1.b38149f0-28ae-11ef-8dec-525400ab18e6.1900c339c0f",
            # "bounce_address": "<EMAIL>",
            "from": {"address": "<EMAIL>", "name": "Health Unwired"},
            "to": email_list,
        }

        headers = {
            "Authorization": f'{os.getenv("ZEPTOMAIL_TOKEN")}',
            "Content-Type": "application/json",
            "Accept": "application/json",
        }
        zeptomail_url = "https://api.zeptomail.in/v1.1/email/template/batch"

        try:
            response = requests.request(
                "POST", zeptomail_url, headers=headers, data=json.dumps(payload2)
            )
            print(
                f"ask reviewwwwwwww mail send-----------{response.status_code}-------{response.content}--------"
            )
            response_status = (
                True
                if response.status_code == 200
                else True if response.status_code in [201, 202] else False
            )
            print(f"--------email_send_done-------{response_status}")
        except HTTPError as e:
            print(f"exception-----------{e.to_dict}")
            print(f"--------email_send_failed-------{response_status}")
    #  Send the deletion notification to the admin
    else:
        u_email = CuUser.objects.filter(groups__id__exact=3)
        email_list = []
        link_a = os.getenv("NEXT_CANCER_UNWIRED_ADMIN_APP") + "/usermanagement"

        for x in u_email:
            email_list.append({
                "email_address": {"address": x.email, "name": x.name},
                "merge_info": {
                    "name": x.name,
                    "u_name": u_name,
                    "reason": deleted_reason,
                    "link": link_a,
                    "fb_url": os.getenv("fb_url"),
                    "insta_url": os.getenv("insta_url"),
                    "twitter_url": os.getenv("twitter_url"),
                    "linkedin_url": os.getenv("linkedin_url"),
                    "youtube_url": os.getenv("youtube_url"),
                },
            })

        payload_del = {
            # Replace with actual ZeptoMail template key for deletion
            "template_key": "2518b.41c485fda42f6e5f.k1.b38149f0-28ae-11ef-8dec-525400ab18e6.DELETE_TEMPLATE_ID",
            "from": {"address": "<EMAIL>", "name": "Health Unwired"},
            "to": email_list,
        }

        headers = {
            "Authorization": f'{os.getenv("ZEPTOMAIL_TOKEN")}',
            "Content-Type": "application/json",
            "Accept": "application/json",
        }

        try:
            response = requests.request(
                "POST", "https://api.zeptomail.in/v1.1/email/template/batch", headers=headers, data=json.dumps(payload_del)
            )
            print(
                f"Deletion email sent: {response.status_code} - {response.content}")
        except HTTPError as e:
            print(f"Deletion email failed: {e}")

# added


class ExpertDeactivateView(generics.UpdateAPIView):
    serializer_class = CuUserSerializer
    lookup_field = "email"
    queryset = CuUser.objects.all()

    def put(self, request, *args, **kwargs):
        approval_status = request.data.get("approval")
        reason_text = request.data.get(
            "deactivation_reason") or request.data.get("deletion_reason")

        if approval_status not in ["self_deactivation", "self_deletion"]:
            return JsonResponse({"message": "Not a valid approval status"}, status=400)

        if not reason_text:
            return JsonResponse({"message": "Reason is required."}, status=400)

        try:
            user = CuUser.objects.get(email__exact=self.kwargs["email"])
        except CuUser.DoesNotExist:
            return JsonResponse({"message": "User not found"}, status=404)

        # Get values before any potential updates
        u_name = user.name
        app_url = os.getenv("NEXT_CANCER_UNWIRED_DOCTOR_APP")

        # Update user data
        response = self.partial_update(request, *args, **kwargs)

        if response.status_code == 200:
            try:
                reason_type = "Deactivation" if approval_status == "self_deactivation" else "Deletion"
                reason_category = "user_deactivation" if approval_status == "self_deactivation" else "user_deletion"

                statusreason = StatusReason.objects.create(
                    ExpertId=user,
                    ReasonType=reason_type,
                    ReasonCategory=reason_category,
                    Reason=reason_text,

                )

                if approval_status == "self_deactivation":
                    print("Sending deactivation email...")
                    send_deactivated_email(user.email, u_name, app_url)
                    AddToZoho(user)

                elif approval_status == "self_deletion":
                    print("Sending deletion email...")
                    # send_deactivated_email(
                    #     u_email=user.email,
                    #     u_name=u_name,
                    #     host=None,
                    #     deleted=True,
                    #     deleted_reason=reason_text
                    # )
                    print(f"User {user.email} requested deletion.")

            except Exception as e:
                print(f"❌ Error after update: {str(e)}", exc_info=True)
                return JsonResponse({"message": "Internal error after update."}, status=500)

        return JsonResponse({
            "message": f"User {approval_status.replace('_', ' ')} request submitted successfully."
        }, status=response.status_code)


# added


# added
class ExpertReactivateView(generics.UpdateAPIView):
    permission_classes = []
    serializer_class = CuUserSerializer
    lookup_field = "email"
    queryset = CuUser.objects.all()

    def put(self, request, *args, **kwargs):
        a = {}
        if len(CuUser.objects.filter(email__exact=self.kwargs["email"])) == 0:
            a = {"message": "User doesn't exist with this email address"}
            return Response(a, status=404)
        if request.data["approval"] == "Approval_requested":
            user = CuUser.objects.get(email__exact=self.kwargs["email"])
            if user.approval == "self_deactivation":
                a = self.partial_update(request, *args, **kwargs)
                aa = StatusReason.objects.create(
                    ExpertId=user,
                    Reason=request.data["reactivation_reason"],
                    ReasonType="Reactivation",
                    ReasonCategory="user_reactivation",
                )
                # nn=notifi_admin(self.kwargs['email'])
                print(f"------expert_reactivation_status{a}")
                # inserting doctor data into zoho accounts
                acc_zoho = AddToZoho(
                    CuUser.objects.get(email__exact=self.kwargs["email"])
                )
                # added
                r_a = {"message": "Successfully submitted reactivation request"}
                return Response(r_a, status=200)
            else:
                a = {"message": "You have already requested."}
                return Response(a, status=412)
        else:
            a = {"message": "not a valid approval status"}
            return Response(a, status=412)


class PodcastRequestView(generics.ListCreateAPIView):
    serializer_class = PodcastSerializer

    def get_queryset(self):
        podcast_filter = Podcast.objects.all()

        # Filter by ExpertId
        if self.kwargs.get("ExpertId") == "all":
            podcast_filter = podcast_filter.filter(PodcastStatus=1)
        else:
            podcast_filter = podcast_filter.filter(
                ExpertId=self.kwargs["ExpertId"], PodcastStatus__in=[1, 2, 3]
            )

        if "status" in self.request.GET:
            podcast_filter = podcast_filter.filter(
                PodcastStatus=self.request.GET["status"])

        # Apply additional filters dynamically
        filters = {
            "PodcastCategory": "Category",
            "PodcastTopic__icontains": "search",
            "ExpertId__groups__name": "Role",
            "ExpertId__doctordetails__Experience": "exp",
        }

        for model_field, param in filters.items():
            if param in self.request.GET:
                podcast_filter = podcast_filter.filter(
                    **{model_field: self.request.GET[param]}
                )

        # Handle date filters safely
        if "start_date" in self.request.GET:
            try:
                start_date = timezone.make_aware(
                    parse_datetime(self.request.GET["start_date"]),
                    timezone.get_current_timezone(),
                )
                podcast_filter = podcast_filter.filter(
                    PodcastDate__gte=start_date)
            except Exception as e:
                print(f"Invalid start_date: {e}")

        if "end_date" in self.request.GET:
            try:
                end_date = timezone.make_aware(
                    parse_datetime(self.request.GET["end_date"]),
                    timezone.get_current_timezone(),
                )
                podcast_filter = podcast_filter.filter(
                    PodcastDate__lte=end_date)
            except Exception as e:
                print(f"Invalid end_date: {e}")

        # Handle location filtering safely
        if "location" in self.request.GET:
            podcast_filter = podcast_filter.filter(
                Q(ExpertId__doctordetails__City__icontains=self.request.GET["location"])
                | Q(ExpertId__doctordetails__Country__iexact=self.request.GET["location"])
            )

        return podcast_filter.order_by("-PodcastViews")

    def post(self, r, *args, **kwargs):
        try:
            # Validate ExpertId from request data
            expertId = r.data.get("ExpertId")
            if not expertId:
                return Response({"error": "ExpertId is required"}, status=status.HTTP_400_BAD_REQUEST)

            try:
                # Check if user_id is in request parameters
                if "user_id" in r.GET and r.GET["user_id"] is not None:
                    user_type = get_cu_user_type(r.GET["user_id"])

                    if user_type in ["admin", "child_admin"]:
                        # Handle ThumbnailImage upload if present
                        if "ThumbnailImage" in r.FILES:
                            try:
                                b1 = handle_uploaded_file(
                                    r.FILES["ThumbnailImage"])
                                f_name = b1.split("/")[2]
                                file_url = settings.PROJECT_ROOT + b1

                                file_urls_res = handle_s3_uploaded_file(
                                    f_name, file_url)
                                r.data["ThumbnailImage"] = file_urls_res
                                print(f"urlsssssssssss{file_urls_res}")
                            except Exception as e:
                                logger.error(
                                    f"Error handling ThumbnailImage: {str(e)}")

                        # Create podcast entry
                        a = self.create(r, *args, **kwargs)

                        # Update Podcast status
                        try:
                            b = Podcast.objects.get(id=a.data["id"])
                            b.PodcastStatus = 2
                            b.save()
                            a.data["PodcastStatus"] = b.PodcastStatus
                        except Podcast.DoesNotExist:
                            return Response({"error": "Podcast not found after creation"}, status=status.HTTP_404_NOT_FOUND)

                        return a
                    else:
                        return HttpResponse("You are not authorized to perform this action.", status=401)
            except Exception as e:
                logger.error(
                    f"Error processing admin/child_admin logic: {str(e)}")
                return Response({"error": "An error occurred while checking user permissions."}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            # If not an admin, process normally
            a = self.create(r, *args, **kwargs)

            try:
                noti_check = notification_check("Podcast approval!")
                if noti_check:
                    notification = NotifyAdminForApproval(
                        "Podcast submission!", expertId, "podcasts")
                    print("Podcast submission! notification", notification)
            except Exception as e:
                logger.error(f"Push notification failed: {str(e)}")

            return a

        except Exception as e:
            logger.error(f"Unexpected error in post method: {str(e)}")
            return Response({"error": "An unexpected error occurred"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def get(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        page_number = request.GET.get("page", 1)
        items_per_page = request.GET.get("per_page", 10)

        paginator = Paginator(queryset, items_per_page)
        if int(page_number) not in range(1, paginator.num_pages + 1):
            return HttpResponse("Not a valid page number", status=400)

        paginated_podcasts = paginator.page(page_number)
        podcast_data = []

        for podcast in paginated_podcasts:
            podcast_dict = PodcastSerializer(podcast).data  # Serialize data

            # Handle ThumbnailImage
            if podcast_dict.get("ThumbnailImage"):
                podcast_dict["ThumbnailImage"] = get_s3_signed_url_bykey(
                    podcast_dict["ThumbnailImage"])
            else:
                podcast_dict["ThumbnailImage"] = None

            # Add expert details
            podcast_dict["expert_role"] = get_cu_user_type(podcast.ExpertId.id)
            podcast_dict["prefix"] = podcast.ExpertId.prefix
            podcast_dict["expert_name"] = podcast.ExpertId.name

            # Fetch expert profile photo safely
            doctor_details = DoctorDetails.objects.filter(
                DoctorId=podcast.ExpertId).first()
            if doctor_details and doctor_details.ProfilePhoto:
                podcast_dict["expert_profile_photo"] = get_s3_signed_url_bykey(
                    doctor_details.ProfilePhoto)
            else:
                podcast_dict["expert_profile_photo"] = None

            # Fetch Podcast Section & Category if available
            if podcast.PodcastSectionVal:
                podcast_dict["PodcastSectionName"] = podcast.PodcastSectionVal.SectionName
            if podcast.PodcastCategoryVal:
                podcast_dict["PodcastCategoryVal"] = podcast.PodcastCategoryVal.Category

            podcast_data.append(podcast_dict)

        response_data = {
            "total_items": queryset.count(),
            "total_pages": paginator.num_pages,
            "items": podcast_data,
        }
        return JsonResponse(response_data, safe=False)


# added
class GetAppContentTypeView(generics.RetrieveAPIView):
    serializer_class = ContentTypeSerializer
    queryset = AdminContentManagement.objects.all()
    lookup_field = "id"

    def get(self, request, *args, **kwargs):
        if self.kwargs["id"] == "all":
            if "Category" in request.GET:
                r_t = AdminContentManagement.objects.filter(
                    Category=request.GET["Category"]
                )
                rt = [serialize_model(t, ContentTypeSerializer) for t in r_t]
                return JsonResponse(rt, safe=False)
            else:
                return Response("Please pass Category in get parameters")
        a = self.retrieve(request, *args, **kwargs)
        return a


# added
# added


class ExpertHomepageView(generics.RetrieveAPIView):
    serializer_class = DoctorDetailsSerializer
    queryset = DoctorDetails.objects.all()

    def get(self, request, *args, **kwargs):
        try:
            doctor_id = self.kwargs["DoctorId_id"]

            # ---------------------- Appointment Section ----------------------
            try:
                app_d = Appointments.objects.filter(
                    slot_id__doctor_id__exact=doctor_id)

                # Today's appointments
                t_app = app_d.filter(
                    slot_id__schedule_start_time__gte=(
                        timezone.now() - timedelta(days=1)),
                    slot_id__schedule_end_time__lte=(
                        timezone.now() + timedelta(days=1)),
                    meetingsession__MeetingStatus__exact=0,
                ).count()

                # Completed appointments
                comp_app = app_d.filter(
                    meetingsession__MeetingStatus__exact=2).count()

                # Upcoming appointments
                up_app = app_d.filter(
                    slot_id__schedule_start_time__gte=timezone.now())

                appoint_deet = {
                    "completed_appointments": comp_app,
                    "upcoming_appointments": up_app.count(),
                    "today_upcoming_appointments": t_app,
                }

                # -------------------- Avg/Rate App Section -------------------
                # Weekly average appointments
                avg_week_app = (
                    app_d.filter(
                        slot_id__schedule_start_time__gte=(
                            timezone.now() - timedelta(days=365)),
                        slot_id__schedule_end_time__lte=timezone.now(),
                    ).count() / 52
                )

                # Last week appointments
                last_week_app = app_d.filter(
                    slot_id__schedule_start_time__gte=(
                        timezone.now() - timedelta(days=14)),
                    slot_id__schedule_end_time__lte=(
                        timezone.now() - timedelta(days=7)),
                ).count()

                # Current week appointments
                curr_week_app = app_d.filter(
                    slot_id__schedule_start_time__gt=(
                        timezone.now() - timedelta(days=7)),
                    slot_id__schedule_end_time__lte=timezone.now(),
                ).count()

                # Response time calculations
                avg_res_time = PatientQueries.objects.filter(
                    ReplyTo__in=app_d.values("id"))
                avg_res_count = avg_res_time.count() or 1

                time_diff = sum(
                    (i.QueryTime - PatientQueries.objects.get(id=i.id).QueryTime).total_seconds()
                    for i in avg_res_time
                )

                # Last week response time
                avg_last_week_res_time = avg_res_time.filter(
                    QueryTime__gte=(timezone.now() - timedelta(days=14)),
                    QueryTime__lte=(timezone.now() - timedelta(days=7)),
                )
                last_diff = sum(
                    (i.QueryTime - PatientQueries.objects.get(id=i.id).QueryTime).total_seconds()
                    for i in avg_last_week_res_time
                )

                # Current week response time
                avg_curr_week_res_time = avg_res_time.filter(
                    QueryTime__gt=(timezone.now() - timedelta(days=7))
                )
                curr_diff = sum(
                    (i.QueryTime - PatientQueries.objects.get(id=i.id).QueryTime).total_seconds()
                    for i in avg_curr_week_res_time
                )

                # Percentage calculations
                per_diff = curr_diff - last_diff
                per_diff_status = "increament" if per_diff >= 0 else "decreament"
                per_diff = (per_diff / (last_diff or 1)) * 100

                app_diff = curr_week_app - last_week_app
                app_diff_status = "increament" if app_diff >= 0 else "decreament"
                app_diff = (app_diff / (last_week_app or 1)) * 100

                avg_appoint_sec = {
                    "avg_week_appointments": avg_week_app,
                    "avg_week_appointments_percentage": app_diff,
                    "avg_week_appointments_percentage_status": app_diff_status,
                    "avg_expert_response_time": time_diff / avg_res_count,
                    "avg_response_time_percentage": per_diff,
                    "avg_response_time_percentage_status": per_diff_status,
                }

                # ----------------- Patient Satisfaction Rating --------------
                pat_sto = PatientStories.objects.filter(
                    ApptId_id__in=app_d.values("id"), status__exact=1
                )
                pat_count = pat_sto.count() or 1

                latest_r = 2.5
                if pat_sto.exists():
                    latest_r = max(pat_sto.order_by(
                        "-CurrentTime")[0].Rating, 2.5)

                avg_r = sum(i.Rating for i in pat_sto) / pat_count
                avg_r = max(avg_r, 2.5)

                # Last week ratings
                last_week_pat = pat_sto.filter(
                    CurrentTime__gte=(timezone.now() - timedelta(days=14)),
                    CurrentTime__lte=(timezone.now() - timedelta(days=7)),
                )
                last_r = sum(i.Rating for i in last_week_pat)

                # Current week ratings
                curr_week_pat = pat_sto.filter(
                    CurrentTime__gt=(timezone.now() - timedelta(days=7)),
                    CurrentTime__lte=timezone.now(),
                )
                curr_r = sum(i.Rating for i in curr_week_pat)

                per_r = curr_r - last_r
                per_r_status = "increament" if per_r >= 0 else "decreament"
                per_r = (per_r / (last_r or 1)) * 100

                patient_rating = {
                    "latest_patient_rating": latest_r,
                    "average_rating": avg_r,
                    "avg_rating_percentage": per_r,
                    "avg_rating_percentage_status": per_r_status,
                }

                # ---------------------- Podcast -----------------------------
                pod_deet = Podcast.objects.filter(
                    ExpertId_id__exact=doctor_id, PodcastStatus__exact=2
                )
                pod_count = pod_deet.count()
                latest_pod = {}

                if pod_deet.exists():
                    latest_pod = PodcastSerializer(
                        pod_deet.order_by("-PodcastDate")[0]).data

                    if latest_pod["ThumbnailImage"]:
                        latest_pod["ThumbnailImage"] = get_s3_signed_url_bykey(
                            latest_pod["ThumbnailImage"])

                    if latest_pod["PodcastSectionVal"]:
                        latest_pod["PodcastSectionName"] = PodcastSection.objects.get(
                            id=latest_pod["PodcastSectionVal"]
                        ).SectionName

                    if latest_pod["PodcastCategoryVal"]:
                        latest_pod["PodcastCategoryVal"] = PodcastCategory.objects.get(
                            id=latest_pod["PodcastCategoryVal"]
                        ).Category

                podcast_latest = {
                    "total_podcast": pod_count,
                    "latest_podcast_data": latest_pod
                }

                # ---------------------- Knowledge ---------------------------
                blog_deet = ExpertBlogs.objects.filter(
                    BlogStatus__exact=1, ExpertId_id__exact=doctor_id
                )

                research_papers = DoctorDetails.objects.filter(
                    DoctorId_id__exact=doctor_id)
                research_pap_count = 0
                latest_research_paper = None

                if research_papers.exists():
                    research_papers_list = research_papers[0].ResearchPapers or [
                    ]
                    research_pap_count = len(research_papers_list)
                    if research_pap_count > 0:
                        latest_research_paper = research_papers_list[-1]

                expert_knowledge = {
                    "total_blogs": blog_deet.count(),
                    "latest_blog_data": {},
                    "total_research_papers": research_pap_count,
                    "latest_research_paper": latest_research_paper,
                }

                if blog_deet.exists():
                    z = ExpertBlogsSerializer(
                        blog_deet.order_by("-BlogDateTime")[0]).data

                    # Process blog images
                    if z["BlogImages"]:
                        z["BlogImages"] = [get_s3_signed_url_bykey(
                            i) for i in z["BlogImages"]]

                    if z["BlogSubImage"]:
                        z["BlogSubImage"] = [get_s3_signed_url_bykey(
                            i) for i in z["BlogSubImage"]]

                    if z["BlogBannerImage"]:
                        z["BlogBannerImage"] = get_s3_signed_url_bykey(
                            z["BlogBannerImage"])

                    if z["BlogFeatureImage"]:
                        z["BlogFeatureImage"] = get_s3_signed_url_bykey(
                            z["BlogFeatureImage"])

                    if z["BlogSectionVal"]:
                        z["BlogSectionName"] = BlogSection.objects.get(
                            id=z["BlogSectionVal"]
                        ).SectionName

                    if z["BlogCategoryVal"]:
                        z["BlogCategoryVal"] = BlogCategory.objects.get(
                            id=z["BlogCategoryVal"]
                        ).Category

                    expert_knowledge["latest_blog_data"] = z

                # ----------------- Prescription Section ---------------------
                pending_pres = []
                user_type = get_cu_user_type(doctor_id)

                if user_type in ["researcher", "influencer"]:
                    app_d_filtered = app_d.filter(
                        meetingsession__MeetingStatus__exact=2)
                    irpres = IRPrescription.objects.filter(
                        AppointmentId__slot_id__doctor_id__exact=doctor_id
                    )
                    pending_pres = app_d_filtered.exclude(
                        id__in=irpres.values("AppointmentId_id"))
                elif user_type == "doctor":
                    app_d_filtered = app_d.filter(
                        meetingsession__MeetingStatus__exact=2)
                    pres = Prescription.objects.filter(
                        AppointmentId__slot_id__doctor_id__exact=doctor_id
                    )
                    pending_pres = app_d_filtered.exclude(
                        id__in=pres.values("AppointmentId_id"))

                app_dd = []
                for x in pending_pres:
                    aaa = serialize_model(x, AppointmentsSerializer)
                    aaa["appointment_start_time"] = timezone.localtime(
                        x.slot_id.schedule_start_time, timezone.get_current_timezone()
                    )
                    aaa["appointment_end_time"] = timezone.localtime(
                        x.slot_id.schedule_end_time, timezone.get_current_timezone()
                    )
                    aaa["patient_name"] = x.patient.name
                    app_dd.append(aaa)

                return Response({
                    "appointment_section_details": appoint_deet,
                    "average_appointment_sec": avg_appoint_sec,
                    "patient_rating_section": patient_rating,
                    "podcast_section": podcast_latest,
                    "knowledge_section": expert_knowledge,
                    "pending_prescription_appointments": app_dd,
                })

            except ObjectDoesNotExist as e:
                return Response(
                    {"error": f"Database object not found: {str(e)}"},
                    status=status.HTTP_404_NOT_FOUND
                )
            except Exception as e:
                return Response(
                    {"error": f"Error processing data: {str(e)}"},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )

        except KeyError:
            return Response(
                {"error": "DoctorId_id parameter is required"},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            return Response(
                {"error": f"Unexpected error: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class GetUpdateExpertWalletView(generics.ListCreateAPIView):
    serializer_class = ExpertWalletTransactionsSerializer


    def get_queryset(self):
        try:
            exp_wal = ExpertWalletTransactions.objects.filter(
                WalletId__ExpertId_id__exact=self.kwargs["expert_id"]
            )
            if exp_wal.exists():
                exp_wal = exp_wal.order_by("-ClearedDate")
            else:
                exp_wal = []
            return exp_wal

        except Exception as e:
            return []

    def get(self, request, *args, **kwargs):
        try:
            expert_id = self.kwargs["expert_id"]
            # Get the wallet instance first
            try:
                wallet = ExpertWallet.objects.get(ExpertId_id=expert_id)
            except ExpertWallet.DoesNotExist:
                return Response(
                    {"error": "Wallet not found for this expert"},
                    status=404
                )
            # Get all transactions
            transactions = ExpertWalletTransactions.objects.filter(
                WalletId=wallet)
            if not transactions.exists():
                return Response(
                    {"message": "No transactions found for this wallet"},
                    status=200
                )
            # Get latest transaction
            latest_txn = transactions.order_by('-TransactionDate').first()
            # Calculate pending withdrawals (only debit transactions with status=0)
            pending_withdrawals = transactions.filter(
                TransactionType=1,  # debit
                PaymentStatus=0     # pending
            )
            total_pending = pending_withdrawals.aggregate(
                total=Sum('TransactionAmount')
            )['total'] or 0

            # Calculate total available balance
            total_balance = latest_txn.BalanceAmount + total_pending

            serializer = self.get_serializer(latest_txn)
            data = serializer.data

            # Add calculated fields
            response_data = {
                **data,
                "available_balance": latest_txn.BalanceAmount,
                "withdrawal_requested_amount": total_pending,
                "total_amount": total_balance
            }
            return Response(response_data)

        except Exception as e:

            return Response(
                {
                    "error": "Internal server error",
                    "details": str(e),
                    "type": type(e).__name__
                },
                status=500
            )

    def create(self, request, *args, **kwargs):
        expert_id = self.kwargs["expert_id"]

        # Get latest transaction to determine balance
        last_transaction = ExpertWalletTransactions.objects.filter(
            WalletId__ExpertId__exact=expert_id
        ).order_by("-TransactionDate").first()

        if not last_transaction:
            return "You can't withdraw as you don't have any balance"

        current_balance = last_transaction.BalanceAmount
        requested_amount = int(request.data.get("TransactionAmount", 0))

        if requested_amount <= 0:
            return "Invalid withdrawal amount"

        if requested_amount > current_balance:
            return "Insufficient funds"

        # Subtract the requested amount to get new balance
        new_balance = current_balance - requested_amount

        # Get wallet ID
        wallet_id = last_transaction.WalletId_id

        # Create the withdrawal transaction (pending)
        ExpertWalletTransactions.objects.create(
            WalletId_id=wallet_id,
            TransactionType=1,  # debit
            TransactionAmount=requested_amount,
            CommissionAmount=0,  # No commission on withdrawal
            BalanceAmount=new_balance,
            PaymentStatus=0,  # pending
            TransactionDate=timezone.now(),
            ClearedDate=None

        )

        return "Your withdrawal request has been raised successfully"

    def post(self, request, *args, **kwargs):
        result = self.create(request, *args, **kwargs)
        return Response(result, status=200)

    # def create(self, request, *args, **kwargs):
    #     wallet_obj = ExpertWalletTransactions.objects.filter(
    #         WalletId__ExpertId__exact=self.kwargs["expert_id"]
    #     )
    #     if wallet_obj.exists():
    #         wallet_obj = wallet_obj.order_by("-ClearedDate")[0]
    #         wallet_pending = ExpertWalletTransactions.objects.filter(
    #             WalletId__ExpertId__exact=self.kwargs["expert_id"], PaymentStatus=0
    #         )
    #         total_amount = wallet_pending.aggregate(
    #             total_amount=Sum("TransactionAmount")
    #         )["total_amount"]
    #         if total_amount is None:
    #             total_amount = 0
    #         if (
    #             total_amount >= wallet_obj.BalanceAmount
    #             or request.data["TransactionAmount"] > wallet_obj.BalanceAmount
    #         ):
    #             return "Insufficient funds"
    #         else:
    #             request.data["BalanceAmount"] = (
    #                 wallet_obj.BalanceAmount -
    #                 request.data["TransactionAmount"]
    #             )
    #             request.data["WalletId"] = wallet_obj.WalletId_id
    #             request.data["TransactionType"] = 1
    #             request.data["PaymentStatus"] = 0
    #             return super().create(request, *args, **kwargs)
    #     else:
    #         return "You can't withdraw as you don't have any balance"

    # def post(self, request, *args, **kwargs):
    #     wallet_row = self.create(request, *args, **kwargs)
    #     if type(wallet_row) == str:
    #         wallet_row = Response(wallet_row, status=200)
    #     else:
    #         wallet_row = Response(
    #             "Your withdrawal request has been raised successfully", status=200
    #         )
    #     return wallet_row


# ----------------expert-payment-chart----------------------------------------------
class ExpertWalletChart(generics.ListAPIView):
    serializer_class = ExpertWalletTransactionsSerializer

    def get(self, request, *args, **kwargs):
        exp_wal = ExpertWalletTransactions.objects.filter(
            WalletId__ExpertId__exact=self.kwargs["expert_id"]
        )
        if exp_wal.exists():
            m_onth = 1
            d_list = []
            m_list = []
            s_um = 0
            best_month = 0

            if request.GET["year"] == "curr":
                year = timezone.now().year
                if request.GET["month"] == "all":
                    lm = timezone.now().month
                    lm = lm + 1
                    for x in range(1, lm):
                        l1 = dict()
                        print(x)
                        s_umm = 0
                        w_um = 0
                        res = calendar.monthrange(year, x)
                        p_m = ""
                        if x < 10:
                            p_m = "0" + str(x)
                        else:
                            p_m = str(x)
                        start_date = timezone.make_aware(
                            parse_datetime(str(year) + "-" + p_m + "-01"),
                            timezone.get_current_timezone(),
                        )
                        print(start_date)
                        end_date = timezone.make_aware(
                            parse_datetime(str(year) + "-" +
                                           p_m + "-" + str(res[1])),
                            timezone.get_current_timezone(),
                        )
                        print(end_date)
                        withdrawal_amount = exp_wal.filter(
                            PaymentStatus__exact=1,
                            TransactionType__exact=1,
                            ClearedDate__gte=start_date,
                            ClearedDate__lte=end_date,
                        )
                        amount_credited = exp_wal.filter(
                            TransactionDate__gte=start_date,
                            TransactionDate__lte=end_date,
                            TransactionType__exact=0,
                        )
                        for i in withdrawal_amount:
                            w_um += int(i.TransactionAmount)
                        for i in amount_credited:
                            s_um += int(i.TransactionAmount)
                            s_umm += int(i.TransactionAmount)
                        l1.update(
                            {
                                str(calendar.month_name[x]): {
                                    "credited_amount": s_umm,
                                    "withdrawal_amount": w_um,
                                }
                            }
                        )
                        if s_umm > m_onth:
                            best_month = p_m
                            m_onth = s_umm
                        d_list.append(l1)
                else:
                    l1 = dict()
                    s_umm = 0
                    w_um = 0
                    p_m = request.GET["month"]
                    res = calendar.monthrange(year, int(p_m))
                    start_date = timezone.make_aware(
                        parse_datetime(str(year) + "-" + p_m + "-01"),
                        timezone.get_current_timezone(),
                    )
                    print(start_date)
                    end_date = timezone.make_aware(
                        parse_datetime(str(year) + "-" +
                                       p_m + "-" + str(res[1])),
                        timezone.get_current_timezone(),
                    )
                    print(end_date)
                    withdrawal_amount = exp_wal.filter(
                        PaymentStatus__exact=1,
                        TransactionType__exact=1,
                        ClearedDate__gte=start_date,
                        ClearedDate__lte=end_date,
                    )
                    amount_credited = exp_wal.filter(
                        TransactionDate__gte=start_date,
                        TransactionDate__lte=end_date,
                        TransactionType__exact=0,
                    )
                    for i in withdrawal_amount:
                        w_um += int(i.TransactionAmount)
                    for i in amount_credited:
                        s_umm += int(i.TransactionAmount)
                    l1.update(
                        {
                            str(calendar.month_name[x]): {
                                "credited_amount": s_umm,
                                "withdrawal_amount": w_um,
                            }
                        }
                    )
                    d_list.append(l1)
            else:
                year = int(request.GET["year"])
                lm = 12
                if request.GET["month"] == "all":
                    lm = 12
                    for x in range(1, lm + 1):
                        l1 = dict()
                        s_umm = 0
                        w_um = 0
                        print(x)
                        res = calendar.monthrange(year, x)
                        p_m = ""
                        if x < 10:
                            p_m = "0" + str(x)
                        else:
                            p_m = str(x)
                        start_date = timezone.make_aware(
                            parse_datetime(str(year) + "-" + p_m + "-01"),
                            timezone.get_current_timezone(),
                        )
                        print(start_date)
                        end_date = timezone.make_aware(
                            parse_datetime(str(year) + "-" +
                                           p_m + "-" + str(res[1])),
                            timezone.get_current_timezone(),
                        )
                        print(end_date)
                        withdrawal_amount = exp_wal.filter(
                            PaymentStatus__exact=1,
                            TransactionType__exact=1,
                            ClearedDate__gte=start_date,
                            ClearedDate__lte=end_date,
                        )
                        amount_credited = exp_wal.filter(
                            TransactionDate__gte=start_date,
                            TransactionDate__lte=end_date,
                            TransactionType__exact=0,
                        )
                        for i in withdrawal_amount:
                            w_um += int(i.TransactionAmount)
                        for i in amount_credited:
                            s_um += int(i.TransactionAmount)
                            s_umm += int(i.TransactionAmount)
                        l1.update(
                            {
                                str(calendar.month_name[x]): {
                                    "credited_amount": s_umm,
                                    "withdrawal_amount": w_um,
                                }
                            }
                        )
                        if s_umm > m_onth:
                            best_month = p_m
                            m_onth = s_umm
                        d_list.append(l1)
                else:
                    l1 = dict()
                    s_umm = 0
                    w_um = 0
                    p_m = request.GET["month"]
                    res = calendar.monthrange(year, int(p_m))
                    start_date = timezone.make_aware(
                        parse_datetime(str(year) + "-" + p_m + "-01"),
                        timezone.get_current_timezone(),
                    )
                    print(start_date)
                    end_date = timezone.make_aware(
                        parse_datetime(str(year) + "-" +
                                       p_m + "-" + str(res[1])),
                        timezone.get_current_timezone(),
                    )
                    print(end_date)
                    withdrawal_amount = exp_wal.filter(
                        PaymentStatus__exact=1,
                        TransactionType__exact=1,
                        ClearedDate__gte=start_date,
                        ClearedDate__lte=end_date,
                    )
                    amount_credited = exp_wal.filter(
                        TransactionDate__gte=start_date,
                        TransactionDate__lte=end_date,
                        TransactionType__exact=0,
                    )
                    for i in withdrawal_amount:
                        w_um += int(i.TransactionAmount)
                    for i in amount_credited:
                        s_um += int(i.TransactionAmount)
                        s_umm += int(i.TransactionAmount)
                    l1.update(
                        {
                            str(calendar.month_name[p_m]): {
                                "credited_amount": s_umm,
                                "withdrawal_amount": w_um,
                            }
                        }
                    )
                    d_list.append(l1)
            if request.GET["month"] == "all":
                avg_month = s_um / 12
                avg_week = s_um / 52
                return JsonResponse(
                    {
                        "data": d_list,
                        "avg_week": avg_week,
                        "avg_month": avg_month,
                        "best_month": calendar.month_name[int(best_month)],
                    }
                )

            return JsonResponse(d_list, safe=False)
        else:
            exp_wal = "No transactions has been done yet."
        return Response(exp_wal)


# ----------expert ranking-------------------------------------
class GetTopAuthorView(views.APIView):
    permission_classes = []

    def get(self, request, *args, **kwargs):
        ins = []
        if self.kwargs["id"] == "all":
            ins = ExpertRank.objects.all()
        else:
            ins = ExpertRank.objects.filter(
                ExpertId_id__exact=self.kwargs["id"])
        data = []
        for x in ins:
            l1 = dict()
            user_data = filter_user_data(x.ExpertId)
            if user_data["doctor_other_details"]["ProfilePhoto"] is not None:
                user_data["doctor_other_details"]["ProfilePhoto"] = (
                    get_s3_signed_url_bykey(
                        user_data["doctor_other_details"]["ProfilePhoto"]
                    )
                )
            l1.update({"expert_rank": x.rank, "expert_details": user_data})
            data.append(l1)
        return JsonResponse(data, safe=False)


# ----------random expert selection-------------------------------------
class GetRandomExpertView(views.APIView):
    permission_classes = []

    def get(self, request, *args, **kwargs):
        ins = RandomExpert.objects.all()
        data_list = []
        for x in ins:
            print(
                f"expertssssssssssssssssssss{x.ExpertId_id}--------------------")
            data = dict()
            a = get_user_model().objects.get(id=x.ExpertId_id).doctordetails
            f_data = filter_user_data(
                get_user_model().objects.get(id=x.ExpertId_id))

            if f_data["doctor_other_details"]["ProfilePhoto"] is not None:
                new_obj_url = get_s3_signed_url_bykey(
                    f_data["doctor_other_details"]["ProfilePhoto"]
                )
                f_data["doctor_other_details"]["ProfilePhoto"] = new_obj_url

            if f_data["doctor_other_details"]["IntVideoUrl"] is not None:

                a_i = []
                b = f_data["doctor_other_details"]["IntVideoUrl"]
                for i in b:
                    new_obj_url = get_s3_signed_url_bykey(i)
                    a_i.append(new_obj_url)
                    print(f"ddd details video--------{new_obj_url}")
                f_data["doctor_other_details"]["IntVideoUrl"] = a_i

            # stories
            Apps = Appointments.objects.filter(
                slot_id__doctor__exact=x.ExpertId_id)
            stories_count = 0
            rating = 0
            for z in Apps:
                stories = z.patientstories_set
                stories_count += stories.count()
                for v in stories.all():
                    rating += v.Rating

            rating_in_number = int(
                rating / stories_count) if stories_count != 0 else 0
            print(f"appssssssssssssssss{stories_count}-----------{Apps}---")
            # stories ends
            data.update(f_data)
            data.update({"patient_stories": stories_count,
                        "rating": rating_in_number})

            data_list.append(data)
            print(
                f"appssssssssssssssss1111111111111{data_list}---------------")

        return JsonResponse({"expert_data": data_list})


# ----------list common topics-------------------------------------
class GetCommonTopicsView(generics.ListAPIView):
    permission_classes = []
    queryset = CommonTopics.objects.all()
    serializer_class = CommonTopicsSerializer

    def get(self, request, *args, **kwargs):
        data = self.list(request, *args, **kwargs)
        return data


# ----------get selected content-------------------------------------
class GetSelectContentView(views.APIView):
    permission_classes = []

    def get(self, request, *args, **kwargs):
        ins = []
        if self.kwargs["content_type"] == "all":
            ins = ContentSelection.objects.all()
        else:
            ins = ContentSelection.objects.filter(
                category__exact=self.kwargs["content_type"]
            )
        data_list = []
        for x in ins:
            print(f"expertssssssssssssssssssss{x}--------------------")
            data = dict()
            data.update({"selected_content_id": x.id})
            data.update({"selected_content_category": x.category})
            data.update({"selected_content_rank": x.rank})

            if x.category == "ExpertReviews":
                if x.ExpertId:

                    a = get_user_model().objects.get(id=x.ExpertId_id).doctordetails
                    f_data = filter_user_data(
                        get_user_model().objects.get(id=x.ExpertId_id)
                    )
                    a_data = dict()
                    a_data.update({"expert_name": f_data["name"]})
                    a_data.update({"expert_prefix": f_data["prefix"]})
                    a_data.update({"expert_role": f_data["role"]})
                    a_data.update(
                        {
                            "expert_profile_photo": f_data["doctor_other_details"][
                                "ProfilePhoto"
                            ]
                        }
                    )
                    if f_data["doctor_other_details"]["ProfilePhoto"] is not None:
                        new_obj_url = get_s3_signed_url_bykey(
                            f_data["doctor_other_details"]["ProfilePhoto"]
                        )
                        f_data["doctor_other_details"]["ProfilePhoto"] = new_obj_url
                        a_data.update(
                            {
                                "expert_profile_photo": f_data["doctor_other_details"][
                                    "ProfilePhoto"
                                ]
                            }
                        )
                    data.update({"expert_details": a_data})
                    data.update(
                        {
                            "content_details": ExpertFeedbackSerializer(
                                ExpertFeedback.objects.get(id__exact=x.c_id)
                            ).data
                        }
                    )
                else:
                    data.update({"expert_details": None})

            elif x.category == "Testimonials":
                p_id = PatientStories.objects.filter(id__exact=x.c_id)[0]
                p1 = Appointments.objects.get(id__exact=p_id.ApptId_id).patient
                a_data = dict()
                a_data.update({"patient_name": p1.name})
                a_data.update({"patient_prefix": p1.prefix})
                new_obj_url = get_s3_signed_url_bykey(
                    p1.patientdetails.ProfilePhoto)
                a_data.update({"patient_photo": new_obj_url})
                data.update({"patient_details": a_data})
                data.update(
                    {
                        "content_details": PatientStoriesSerializer(
                            PatientStories.objects.get(id__exact=x.c_id)
                        ).data
                    }
                )
                print(
                    PatientStoriesSerializer(
                        PatientStories.objects.filter(id__exact=x.c_id)
                    ).data
                )
            data_list.append(data)
        return JsonResponse({"selected_data": data_list})


def GetCountryBankParams(country, currency):
    payload = "{}"
    headers = {
        "Content-Type": "application/json",
        "x-api-key": os.getenv("AIRWALLEX_API_KEY"),
        "x-client-id": os.getenv("AIRWALLEX_CLIENT_ID"),
    }

    url1 = f"https://api-demo.airwallex.com/api/v1/authentication/login"

    res = requests.post(url1, json=payload, headers=headers)
    data = res.json()
    ACCESS_TOKEN = data["token"]
    print("Access token", ACCESS_TOKEN)
    payload = {"source": "condition", "bank_country_code": country,
               "amount_currency": currency,
               "entity_type": "PERSONAL",
               "local_clearing_system": "ACH",
               "payment_method": "LOCAL"}
    AIRWALLEX_API_URL = "https://api-demo.airwallex.com/api/v1/beneficiary_form_schemas/generate"
    url = f"{AIRWALLEX_API_URL}"
    headers = {"Authorization": f"Bearer {ACCESS_TOKEN}",
               "headers": "[object Object]",
               "Content-Type": "application/json", }

    response = requests.post(url, json=payload, headers=headers)
    print(response.json())
    if response.status_code == 200:
        data = response.json()
        required_fields = data.get("fields", [])
        return JsonResponse({"fields": required_fields, "country_code": country}, status=200)
    return JsonResponse({"error": "Failed to fetch bank parameters"}, status=500)


class GetBankParamsView(views.APIView):
    def get(self, request, *args, **kwargs):
        country = request.GET['country']
        currency = request.GET['currency']
        a = GetCountryBankParams(country, currency)
        if a:
            return a
        else:
            return Response("There is some error fetching the country's bank params.")


def AddBankDetails(bank_details):
    payload = "{}"
    headers = {
        "Content-Type": "application/json",
        "x-api-key": os.getenv("AIRWALLEX_API_KEY"),
        "x-client-id": os.getenv("AIRWALLEX_CLIENT_ID"),
    }

    url1 = f"https://api-demo.airwallex.com/api/v1/authentication/login"

    res = requests.post(url1, json=payload, headers=headers)
    data = res.json()
    ACCESS_TOKEN = data["token"]
    AIRWALLEX_BENEFICIARY_URL = "https://api-demo.airwallex.com/api/v1/beneficiaries/create"
    headers = {
        "Authorization": f"Bearer {ACCESS_TOKEN}",
        "Content-Type": "application/json"
    }

    response = requests.post(AIRWALLEX_BENEFICIARY_URL,
                             json=bank_details, headers=headers)

    if response.status_code == 201:
        return JsonResponse({"message": "Beneficiary created successfully!"}, status=201)
    else:
        return JsonResponse({"error": response.json()}, status=400)


class ExpertBankDetailsView(generics.ListAPIView, generics.CreateAPIView, generics.RetrieveAPIView, generics.DestroyAPIView):
    queryset = ExpertBankDetails.objects.all()
    serializer_class = ExpertBankDetailsSerializer
    permission_classes = []
    lookup_field = "id"

    def create(self, request, *args, **kwargs):
        user = self.request.user
        try:
            b = AddBankDetails(request.data['bank_details'])
            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            instance = serializer.save(account_holder=self.request.user)
            response_serializer = self.get_serializer(instance)
            return generate_response(
                success=True,
                message="Bank account created successfully.",
                data=response_serializer.data,
                status_code=status.HTTP_200_OK,
            )

        except serializers.ValidationError as e:
            error_messages = "; ".join(
                f"{field}: {', '.join(errors)}" for field, errors in e.detail.items()
            )
            return generate_response(
                success=False,
                message=error_messages,
                status_code=status.HTTP_400_BAD_REQUEST,
            )

        except Exception as e:
            return generate_response(
                success=False,
                message="An error occurred while creating the bank account.",
                data={"details": str(e)},
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def get(self, request, *args, **kwargs):
        try:
            if 'id' in kwargs:
                response = self.retrieve(request, *args, **kwargs)
            else:
                response = self.list(request, *args, **kwargs)

            return generate_response(
                success=True,
                data=response.data,
                status_code=status.HTTP_200_OK,
            )

        except Exception as e:
            return generate_response(
                success=False,
                message="An error occurred while fetching bank account.",
                data={"details": str(e)},
                status_code=status.HTTP_400_BAD_REQUEST,
            )

    def delete(self, request, *args, **kwargs):
        try:
            if 'id' not in kwargs:
                return generate_response(
                    success=False,
                    message="The 'id' parameter is required for deletion.",
                    status_code=status.HTTP_400_BAD_REQUEST,
                )

        # Check if the record exists
            instance = self.get_object()
            self.perform_destroy(instance)

            return generate_response(
                success=True,
                message="User Bank deleted successfully.",
                status_code=status.HTTP_200_OK,
            )

        except Exception as e:
            return generate_response(
                success=False,
                message="An error occurred while fetching bank account.",
                data={"details": str(e)},
                status_code=status.HTTP_400_BAD_REQUEST,
            )


class UserBankAccountsView(APIView):
    def get(self, request, user_id, *args, **kwargs):
        try:
            user_accounts = ExpertBankDetails.objects.filter(
                account_holder_id=user_id)

            # Serialize the data
            serializer = ExpertBankDetailsSerializer(user_accounts, many=True)
            return generate_response(
                success=True,
                data=serializer.data,
                status_code=status.HTTP_200_OK,
            )
        except Exception as e:
            return generate_response(
                success=False,
                message="An error occurred while fetching bank account.",
                data={"details": str(e)},
                status_code=status.HTTP_400_BAD_REQUEST,
            )


class UpdateExpertBankDetailsView(generics.UpdateAPIView):
    queryset = ExpertBankDetails.objects.all()
    serializer_class = ExpertBankDetailsSerializer
    permission_classes = []
    lookup_field = "id"

    def update(self, request, *args, **kwargs):
        try:
            partial = kwargs.pop('partial', False)
            instance = self.get_object()

            serializer = self.get_serializer(
                instance, data=request.data, partial=partial)
            serializer.is_valid(raise_exception=True)
            serializer.save()

            return generate_response(
                success=True,
                message="Bank details updated successfully.",
                status_code=status.HTTP_200_OK,
                data=serializer.data,
            )

        except ValidationError as e:
            return generate_response(
                success=False,
                message="Validation Error",
                status_code=status.HTTP_400_BAD_REQUEST,
            )

        except Exception as e:
            return generate_response(
                success=False,
                message="An error occurred while updating the bank account.",
                data={"details": str(e)},
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class MedicalSpecialtyListCreateView(generics.ListCreateAPIView):
    """Handles Listing & Creating Medical Specialties"""
    queryset = MedicalSpecialty.objects.all()
    serializer_class = MedicalSpecialtySerializer
    permission_classes = []


class MedicalSpecialtyRetrieveUpdateDeleteView(generics.RetrieveUpdateDestroyAPIView):
    """Handles Retrieve, Update & Delete"""
    queryset = MedicalSpecialty.objects.all()
    serializer_class = MedicalSpecialtySerializer
    permission_classes = []
    lookup_field = 'id'
