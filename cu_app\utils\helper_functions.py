from django.contrib.auth import get_user_model
from cu_app.models.doctor_models import DoctorDetails
from cu_app.models.patient_models import SchedulerSlots
from cu_app.models.misc_models import Pricing
from django.forms.models import model_to_dict


def get_cu_user_type(a):
    try:
        b = get_user_model().objects.get(id=a)
        u_groups = b.groups.all()
        if len(u_groups) >= 1:

            return u_groups[0].name
        else:
            return "no valid user found"
    except Exception as e:
        return str(e)


def calculate_fees(doctor_id, slot_id,amount=None):
    doctor_details = DoctorDetails.objects.get(DoctorId=doctor_id)
    pricing = Pricing.objects.last()

    # print("doctor_details", model_to_dict(doctor_details))

    # Defensive defaults
    doctor_fees = amount if amount is not None else (doctor_details.ConsultationFees or 0)
    platform_charges = pricing.PlatformCharges or 0
    transaction_percentage = pricing.TransactionCharges or 0

    slot = SchedulerSlots.objects.get(doctor_id=doctor_id, id=slot_id)
    duration = (slot.schedule_end_time -
                slot.schedule_start_time).total_seconds() / 1800

    base_charges = duration * doctor_fees + platform_charges
    transaction_charges = base_charges * transaction_percentage / 100
    final_charge = base_charges + transaction_charges

    return {
        "doctor_fees": doctor_fees,
        "platform_charges": platform_charges,
        "transaction_charges": transaction_charges,
        "final_charge": final_charge,
    }
