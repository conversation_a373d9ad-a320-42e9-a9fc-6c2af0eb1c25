
from django.conf import settings
import json
import logging
import os
from django.contrib.auth import get_user_model
from firebase_admin.messaging import Message, Notification, WebpushConfig, WebpushFCMOptions
from fcm_django.models import FCMDevice
from django.utils import timezone
from cu_app.models.misc_models import PushNotifications
import requests
from cu_app.models.misc_models import NotificationType
from cu_app.models.patient_models import CuUser
from python_http_client.exceptions import HTTPError
from cu_app.models.patient_models import SchedulerSlots
from cu_app.utils.helper_functions import get_cu_user_type

logger = logging.getLogger(__name__)


def AdminApprovePushNotification(n_name, u_id, r_status, admin_id, type_n):
    try:
        current_time = timezone.now()
        user = get_user_model().objects.filter(id=u_id).first()
        admin = get_user_model().objects.filter(id=admin_id).first()

        if not user or not admin:
            logger.error("User or admin details not found")
            return False

        u_email, u_name = user.email, user.name
        a_name = admin.name

        r_name = "approved"
        l_ink = os.getenv('NEXT_CANCER_UNWIRED_DOCTOR_APP') + '/uploads'

        # Define request types and status mappings
        request_types = {
            'Blog approval!': ("blog approval request to admin", 2, 'rejected'),
            'Podcast approval!': ("requested Podcast", 2, "published"),
            'Consent form approval!': ("consent form approval request to admin", 0, 'rejected'),
            'Introduction video approval!': ("introduction video request to admin", 3, 'rejected'),
            'Review approval!': ("profile review submitted by external patient", 3, 'rejected'),
            'Testimonial approval!': ("story submitted by patient", 3, 'rejected'),
            'Feedback approval!': ("feedback submitted", 0, 'rejected'),
        }

        a_request, status_code, reject_name = request_types.get(
            n_name, (None, None, None))

        if a_request is None:
            logger.error("Invalid approval request type")
            return False

        if r_status == status_code:
            r_name = reject_name

        if type_n == "N":
            try:
                devices = FCMDevice.objects.filter(user_id=u_id)
                if devices.exists():
                    try:
                        notification_rsp = devices.send_message(Message(
                            notification=Notification(
                                title=n_name,
                                body=f'Hi {u_name}, Your {a_request} has been {r_name} by admin.'
                            ),
                            webpush=WebpushConfig(
                                fcm_options=WebpushFCMOptions(link=l_ink))
                        ))
                        print("notification_rsp", notification_rsp)
                        logger.info(
                            f"Push notification sent to all devices of user {u_id}")
                    except Exception as e:
                        logger.error(
                            f"Push notification failed for user {u_id}: {e}")

                else:
                    logger.warning(
                        f"No registered FCM device found for user {u_id}, skipping push notification.")

                # Save notification to database
                PushNotifications.objects.create(
                    UserId=u_id,
                    NotificationTime=current_time,
                    Title=n_name,
                    Body=f'Hi {u_name}, Your {a_request} has been {r_name} by admin.',
                    Link=l_ink
                )

                # Send notification to admin
                devices = FCMDevice.objects.filter(user_id=admin_id)
                if devices.exists():
                    try:
                        devices.send_message(Message(
                            notification=Notification(
                                title=n_name,
                                body=f'Hi {a_name}, You have {r_name} {a_request} of expert {u_name}.'
                            ),
                            webpush=WebpushConfig(fcm_options=WebpushFCMOptions(
                                link=os.getenv('NEXT_CANCER_UNWIRED_ADMIN_APP') + '/usermanagement/experts'))
                        ))
                    except Exception as e:
                        logger.error(
                            f"Push notification failed for user {u_id}: {e}")
                else:
                    logger.warning(
                        f"No registered FCM device found for admin {admin_id}, skipping push notification.")

                # Save notification for admin
                PushNotifications.objects.create(
                    UserId=admin_id,
                    NotificationTime=current_time,
                    Title=n_name,
                    Body=f'Hi {a_name}, You have {r_name} {a_request} of expert {u_name}.',
                    Link=os.getenv('NEXT_CANCER_UNWIRED_ADMIN_APP') +
                    '/usermanagement/experts'
                )

            except Exception as e:
                logger.error(f"Error sending push notification: {str(e)}")

        if type_n == "E":  # Sending Email
            try:
                payload = {
                    "template_key": "2518b.41c485fda42f6e5f.k1.650134b0-b6ad-11ef-9690-525400b0b0f3.193aec7837b",
                    "from": {"address": "<EMAIL>", "name": "Health Unwired"},
                    "to": [{"email_address": {"address": u_email, "name": u_name.title()}}],
                    "merge_info": {
                        "r_link": l_ink,
                        "name": u_name.title(),
                        "pressLink": "pressLink_value",
                        "r_type": n_name.replace("!", ""),
                        "status": r_name,
                        "fb_url": os.getenv("fb_url"),
                        "insta_url": os.getenv("insta_url"),
                        "twitter_url": os.getenv("twitter_url"),
                        "linkedin_url": os.getenv("linkedin_url"),
                        "youtube_url": os.getenv("youtube_url"),
                    }
                }

                headers = {
                    'Authorization': f'{os.getenv("ZEPTOMAIL_TOKEN")}',
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }

                response = requests.post(
                    "https://api.zeptomail.in/v1.1/email/template", headers=headers, json=payload)
                response.raise_for_status()
                return True

            except Exception as e:
                logger.error(f"Error sending email notification: {str(e)}")

        return True

    except Exception as e:
        logger.error(f"Error in AdminApprovePushNotification: {str(e)}")
        return False


def NotifyAdminForApproval(n_name, expert_id, tab=None):
    try:
        current_time = timezone.now()
        expert = get_user_model().objects.filter(id=expert_id).first()

        if not expert:
            logger.error("Expert details not found")
            return False

        u_name = expert.name

        # Get all admin users
        admins = CuUser.objects.filter(is_admin__exact=1)

        if not admins.exists():
            logger.error("Admin users not found")
            return False

        # Notification message content
        request_types = {
            'Blog submission!': f"Expert {u_name} has submitted a blog for approval.",
            'Podcast submission!': f"Expert {u_name} has submitted a podcast for approval.",
            'Testimonial submission!': f"Expert {u_name} has submitted a testimonial for approval.",
            'Feedback submission!': f"Expert {u_name} has submitted feedback for approval.",
            'Consent form submission!': f"Expert {u_name} has submitted consent for approval.",
            'Intro video submission!': f"Expert {u_name} has submitted intro video for approval.",
            'External Review submission!': f"Expert {u_name} has submitted external Review for approval.",
            'Profile Approval Request submission!': f"{u_name} has submitted external Review for approval.",
        }

        message_body = request_types.get(n_name, None)
        if not message_body:
            logger.error("Invalid submission request type")
            return False

        base_url = os.getenv('NEXT_CANCER_UNWIRED_ADMIN_APP') + \
            "/usermanagement/allApprovalRequest"
        link = f"{base_url}?tab={tab}" if tab else base_url

        # Loop through all admins and send notifications
        for admin in admins:
            admin_id = admin.id
            a_name = admin.name
            print(f"Sending notification to admin {a_name} (ID: {admin_id})")

            try:
                # Get FCM devices for the admin
                devices = FCMDevice.objects.filter(user_id=admin_id)
                if devices.exists():
                    for device in devices:
                        print(
                            f"Sending notification to device {device.registration_id}")
                        device.send_message(Message(
                            notification=Notification(
                                title=n_name,
                                body=message_body
                            ),
                            webpush=WebpushConfig(
                                fcm_options=WebpushFCMOptions(link=link)
                            )))
                        logger.info(
                            f"Push notification sent to admin {a_name} (ID: {admin_id})")
                else:
                    logger.warning(
                        f"No registered FCM device found for admin {a_name} (ID: {admin_id})")

            except Exception as e:
                logger.error(
                    f"Error sending push notification to admin {a_name} (ID: {admin_id}): {str(e)}")

        # Save ONE notification to the database (common for all admins)
        primary_admin = admins.first()  # Use the first admin as the primary admin
        PushNotifications.objects.create(
            UserId=primary_admin.id,  # Use the primary admin's ID
            NotificationTime=current_time,
            Title=n_name,
            Body=message_body,
            Link=link
        )

        print("After sending the push noti for bell")
        return True

    except Exception as e:
        logger.error(f"Error in NotifyAdminForApproval: {str(e)}")
        return False


def notification_check(n_name):
    noti_check = NotificationType.objects.filter(
        NotificationName=n_name, CategoryType="N"
    )
    if noti_check.exists():
        if (
            NotificationType.objects.filter(NotificationName=n_name, CategoryType="N")[
                0
            ].ActiveStatus
            == 1
        ):
            return True
        return False
    else:
        noti_res = NotificationType.objects.create(
            NotificationName=n_name, CategoryType="N"
        )
        return True


def email_check(e_name):
    email_check = NotificationType.objects.filter(
        NotificationName=e_name, CategoryType="E"
    )
    if email_check.exists():
        if (
            NotificationType.objects.filter(NotificationName=e_name, CategoryType="E")[
                0
            ].ActiveStatus
            == 1
        ):
            return True
        return False
    else:
        email_res = NotificationType.objects.create(
            NotificationName=e_name, CategoryType="E"
        )
        return True


def SendPushReplyQuery(reply_id, app_url, u_id, u_name):
    current_time = timezone.now()
    device = FCMDevice.objects.filter(user_id__exact=u_id)
    # print(f"fcm devices----{device}---{type(device)}")
    img_url = settings.MEDIA_ROOT + "\logo.png"
    data = ""
    print(data)
    if data:
        a = ""
    else:
        a = device.send_message(
            Message(
                notification=Notification(
                    title="Query replied!",
                    body=f"Hi {u_name}, Your query has been replied by the expert.",
                ),
                webpush=WebpushConfig(
                    fcm_options=WebpushFCMOptions(link=app_url)),
            )
        )
        res = PushNotifications.objects.create(
            UserId=u_id,
            NotificationTime=current_time,
            Title="Query replied!",
            Body=f"Hi {u_name}, Your query has been replied by the expert.",
            Link=app_url,
        )
        print(f"push----{a}")
    return a


def SendEmailReplyQuery(reply_id, app_url, u_id, u_name):

    u_email = CuUser.objects.filter(id__exact=u_id)[0].email
    payload = {
        "template_key": "2518b.41c485fda42f6e5f.k1.a30b7940-2e0f-11ef-857c-525400674725.1902f737ed4",
        # "bounce_address": "<EMAIL>",
        "from": {"address": "<EMAIL>", "name": "Health Unwired"},
        "to": [
            {
                "email_address": {
                    "address": u_email,
                    "name": u_email,
                }
            }
        ],
        "merge_info": {"u_name": u_name,
                       "link": app_url,
                       "fb_url": os.getenv("fb_url"),
                       "insta_url": os.getenv("insta_url"),
                       "twitter_url": os.getenv("twitter_url"),
                       "linkedin_url": os.getenv("linkedin_url"),
                       "youtube_url": os.getenv("youtube_url"), },
    }

    headers = {
        "Authorization": f'{os.getenv("ZEPTOMAIL_TOKEN")}',
        "Content-Type": "application/json",
        "Accept": "application/json",
    }
    zeptomail_url = "https://api.zeptomail.in/v1.1/email/template"
    try:
        response = requests.request(
            "POST", zeptomail_url, headers=headers, data=json.dumps(payload)
        )
        print(
            f"pw mail send-----------{response.status_code}-------{response.content}--------"
        )
        response_status = (
            True
            if response.status_code == 200
            else True if response.status_code in [201, 202] else False
        )
        return response_status
    except HTTPError as e:
        print(f"exception-----------{e.to_dict}")
        return response_status


def CancelPushNotification(u_id, d_id):
    current_time = timezone.now()
    u_name = get_user_model().objects.get(id__exact=u_id).name
    uu_name = get_user_model().objects.get(id__exact=d_id).name
    print(f"in user nameeeeeeeeeeeeeeeeeeeee{u_name}")
    # doctor notification
    device = FCMDevice.objects.filter(user_id__exact=u_id)
    print(f"in reminderrrrrrrrrrr{device}")
    # print(f"fcm devices----{device}---{type(device)}")
    img_url = settings.MEDIA_ROOT + "\logo.png"
    a = device.send_message(Message(notification=Notification(title='Appointment cancellation!',
                                                              body=f'Hi {uu_name}, Your upcoming appointment with {u_name} is cancelled.'),
                                    webpush=WebpushConfig(fcm_options=WebpushFCMOptions(
                                        link=os.getenv('NEXT_CANCER_UNWIRED_DOCTOR_APP') + '/appointment'))))
    res = PushNotifications.objects.create(UserId=u_id, NotificationTime=current_time,
                                           Title='Appointment cancellation!',
                                           Body=f'Hi {uu_name}, Your upcoming appointment with {u_name} is cancelled.', Link=os.getenv('NEXT_CANCER_UNWIRED_DOCTOR_APP') + '/appointment')

    # patient notification
    device = FCMDevice.objects.filter(user_id__exact=d_id)
    print(f"in reminderrrrrrrrrrr{device}")
    # print(f"fcm devices----{device}---{type(device)}")
    img_url = settings.MEDIA_ROOT + "\logo.png"
    a = device.send_message(Message(notification=Notification(title='Appointment cancellation!',
                                                              body=f'Hi {u_name}, Your upcoming appointment with {uu_name} is cancelled.'),
                                    webpush=WebpushConfig(fcm_options=WebpushFCMOptions(
                                        link=os.getenv('NEXT_CANCER_UNWIRED_PATIENT_APP') + '/myprofile?tab=appointments'))))
    res = PushNotifications.objects.create(UserId=d_id, NotificationTime=current_time,
                                           Title='Appointment cancellation!',
                                           Body=f'Hi {u_name}, Your upcoming appointment with {uu_name} is cancelled.', Link=os.getenv('NEXT_CANCER_UNWIRED_PATIENT_APP') + '/myprofile?tab=appointments')

    return a


def CancelEmail(patient_id, doctor_id, slot_id, app_id, cancellation_reason):

    slot_data = SchedulerSlots.objects.filter(id__exact=slot_id)[0]

    print(f'-----------------------------slot_data----------{slot_data}')
    start_date = slot_data.schedule_start_time
    end_date = slot_data.schedule_end_time
    converted_time1 = timezone.localtime(
        start_date, timezone.get_current_timezone())
    print(f'-------------------coverted_time---------{converted_time1}')
    converted_time2 = timezone.localtime(
        end_date, timezone.get_current_timezone())
    print(f'-------------------coverted_time---------{converted_time2}')

    s_date = converted_time1.date()
    ss_time = converted_time1.time()
    b = str(ss_time).split(":")
    s_time = b[0]+":"+b[1]

    ee_time = converted_time2.time()
    b = str(ee_time).split(":")
    e_time = b[0]+":"+b[1]

    timings = str(str(s_date) + " " + s_time + "-" + e_time)
    expert_role = get_cu_user_type(doctor_id)
    patient_details = CuUser.objects.filter(id__exact=patient_id)[0]
    patient_email = patient_details.email
    patient_name = patient_details.name

    doctor_details = CuUser.objects.filter(id__exact=doctor_id)[0]
    doctor_email = doctor_details.email
    doctor_name = doctor_details.name

    payload1 = {
        "template_key": "2518b.41c485fda42f6e5f.k1.c06d4c50-fe19-11ee-97ae-525400ab18e6.18ef5232595",
        # "bounce_address": "<EMAIL>",
        "from": {
            "address": "<EMAIL>",
            "name": "Health Unwired"
        },
        "to": [
            {
                "email_address": {
                    "address": doctor_email,
                    "name": doctor_name,
                }
            }

        ],
        "merge_info": {
            "user_role": "patient",
            "aa_name": patient_name,
            "a_name": doctor_name,
            "app_timings": timings,
            "app_id": app_id,
            "fb_url": os.getenv("fb_url"),
            "insta_url": os.getenv("insta_url"),
            "twitter_url": os.getenv("twitter_url"),
            "linkedin_url": os.getenv("linkedin_url"),
            "youtube_url": os.getenv("youtube_url"),
            "cancellation_reason": cancellation_reason,
        },
    }

    payload2 = {
        "template_key": "2518b.41c485fda42f6e5f.k1.c06d4c50-fe19-11ee-97ae-525400ab18e6.18ef5232595",
        # "bounce_address": "<EMAIL>",
        "from": {
            "address": "<EMAIL>",
            "name": "Health Unwired"
        },
        "to": [
            {
                "email_address": {
                    "address": patient_email,
                    "name": patient_name
                }
            }

        ],
        "merge_info": {
            "user_role": expert_role,
            "aa_name": doctor_name,
            "a_name": patient_name,
            "app_timings": timings,
            "app_id": app_id,
            "fb_url": os.getenv("fb_url"),
            "insta_url": os.getenv("insta_url"),
            "twitter_url": os.getenv("twitter_url"),
            "linkedin_url": os.getenv("linkedin_url"),
            "youtube_url": os.getenv("youtube_url"),
            "cancellation_reason": cancellation_reason,
        },
    }
    headers = {
        'Authorization': f'{os.getenv("ZEPTOMAIL_TOKEN")}',
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }
    zeptomail_url = "https://api.zeptomail.in/v1.1/email/template"
    try:

        response = requests.request(
            "POST", zeptomail_url, headers=headers, data=json.dumps(payload1))
        print(
            f"verify mail send111-------------{response.status_code}-------{response.content}--------")
        response1 = requests.request(
            "POST", zeptomail_url, headers=headers, data=json.dumps(payload2))
        print(
            f"verify mail send222-----------{response1.status_code}-------{response1.content}--------")

        response_status1 = True if response.status_code == 200 else True if response.status_code in [
            201, 202] else False
        response_status2 = True if response1.status_code == 200 else True if response1.status_code in [
            201, 202] else False
        return response_status1 and response_status2
    except HTTPError as e:
        print(f"meet mail exception-----------{e.to_dict}")
        return response_status1 and response_status2
# added


def CancelAppAdmin(app_id):
    # notification to super_admin
    admin_obj = CuUser.objects.filter(is_admin__exact=1)
    for y in admin_obj:

        device_d = FCMDevice.objects.filter(user_id__exact=y.id)
        print(f"fcm devices----{device_d}---{type(device_d)}")
        img_url = settings.MEDIA_ROOT+"\logo.png"
        current_time = timezone.now()
        a = device_d.send_message(Message(notification=Notification(title='Appointment cancelled!', body=f'Hi {y.name}, Appointment with id -{app_id} has been cancelled.'), webpush=WebpushConfig(
            fcm_options=WebpushFCMOptions(link=os.getenv('NEXT_CANCER_UNWIRED_ADMIN_APP')+'/calendar'))))
        res = PushNotifications.objects.create(UserId=y.id, NotificationTime=current_time,
                                               Title='Appointment cancelled!', Body=f'Hi {y.name}, Appointment with id -{app_id} has been cancelled!', Link=os.getenv('NEXT_CANCER_UNWIRED_ADMIN_APP')+'/calendar')
        print(f"push----{a}")
    return True
# utils.py


def send_cancellation_notifications(appointment_id, patient_id, doctor_id, slot_id):
    try:
        if notification_check('Appointment cancellation'):
            CancelPushNotification(patient_id, doctor_id)

        CancelAppAdmin(appointment_id)

        if email_check('Appointment cancellation'):
            CancelEmail(patient_id, doctor_id, slot_id, appointment_id)

    except Exception as e:
        print(f"Notification error: {str(e)}")
