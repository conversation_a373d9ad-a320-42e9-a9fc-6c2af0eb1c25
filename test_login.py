import requests
import json

# Test login endpoint
url = "http://localhost:8000/api/login/"
data = {
    "email": "<EMAIL>",
    "password": "@Doctor@123#",
    "user_app": "expert"
}

headers = {
    "Content-Type": "application/json"
}

try:
    print("Sending login request...")
    response = requests.post(url, json=data, headers=headers, timeout=30)
    print(f"Status Code: {response.status_code}")
    print(f"Response: {response.text}")

    if response.status_code == 200:
        print("Login successful!")
        response_data = response.json()
        print(f"Login status: {response_data.get('login_status', 'Unknown')}")
        print(f"Token available: {'Yes' if response_data.get('X-AUTH-TOKEN') else 'No'}")
    else:
        print("Login failed!")

except requests.exceptions.Timeout:
    print("Request timed out - the server might be processing notifications")
except requests.exceptions.RequestException as e:
    print(f"Request failed: {e}")
